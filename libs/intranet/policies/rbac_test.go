package policies

import (
	"encoding/json"
	"testing"

	"github.com/samber/do"
)

// go test -v libs/intranet/policies/rbac_test.go libs/intranet/policies/rbac.go -run TestRBACPolicy
func TestRBACPolicy(t *testing.T) {
	var policy RBACPolicy
	if err := json.Unmarshal(rbacJSON, &policy); err != nil {
		t.Fatalf("Failed to unmarshal policy: %v", err)
	}

	tenant := "default"
	service := "ea-service"

	admin := User{
		ID:           "user1234a",
		DepartmentID: "dept4567a",
		Role:         "admin",
	}

	enterpriseArch := User{
		ID:           "user1234e",
		DepartmentID: "dept4567e",
		Role:         "enterprise-architect",
	}

	businessArch := User{
		ID:           "user1234b",
		DepartmentID: "dept4567b",
		Role:         "business-architect",
	}

	applicationArch := User{
		ID:           "user1234a",
		DepartmentID: "dept4567a",
		Role:         "application-architect",
	}

	guest := User{
		ID:           "user123",
		DepartmentID: "dept456",
		Role:         "guest",
	}

	tests := []struct {
		name                    string
		request                 *AuthRequest
		wantErr                 bool
		wantScopeLen            int
		disableScopesValidation bool
	}{
		// Enterprise Architect role tests
		{
			name: "Invalid enterprise-architect request with no actions",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{},
				AllowedScopes: []string{"all"},
				User:          enterpriseArch,
			},
			wantErr:                 true,
			disableScopesValidation: false,
			wantScopeLen:            0,
		},
		{
			name: "Valid enterprise-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          enterpriseArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},

		// Admin role tests
		{
			name: "Invalid admin request with no actions",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 true,
			disableScopesValidation: false,
			wantScopeLen:            0,
		},
		{
			name: "Valid admin request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid admin request to admin path",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		// Business Architect role tests
		{
			name: "Valid business-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid business-architect request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Valid business-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		// Application Architect role tests
		{
			name: "Valid application-architect request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          applicationArch,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		// Guest role tests
		{
			name: "Valid guest request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/wiki",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          applicationArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid guest request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/wiki/",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read cqm module with slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read cqm module without slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read admin module with slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin/",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read admin module without slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := policy.Validate(tt.request, ValidateOptions{
				DisableScopesValidation: tt.disableScopesValidation,
			})

			if (err != nil) != tt.wantErr {
				t.Errorf("RBACPolicy.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if result == nil {
					t.Error("Expected validation result, got nil")
					return
				}

				if result.Role == nil {
					t.Error("Expected role information, got nil")
					return
				}

				if len(result.ActiveScopes) != tt.wantScopeLen {
					t.Errorf("Expected %d active scopes, got %d", tt.wantScopeLen, len(result.ActiveScopes))
				}
			}
		})
	}
}

func TestMapToJwtDataHeader(t *testing.T) {
	tests := []struct {
		name   string
		header map[string]any
		want   JwtDataHeader
	}{
		{
			name: "complete_header_data",
			header: map[string]any{
				"alg":    "ES256",
				"client": "test-client",
				"kid":    "test-key-id",
				"exp":    **********,
				"typ":    "JWT",
				"iss":    "https://cognito.amazonaws.com",
				"signer": "arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/test-alb/**********123456",
			},
			want: JwtDataHeader{
				Alg:    "ES256",
				Client: "test-client",
				Kid:    "test-key-id",
				Exp:    **********,
				Typ:    "JWT",
				ISS:    "https://cognito.amazonaws.com",
				Signer: "arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/test-alb/**********123456",
			},
		},
		{
			name: "partial_header_data",
			header: map[string]any{
				"alg": "ES256",
				"typ": "JWT",
				"exp": **********,
			},
			want: JwtDataHeader{
				Alg: "ES256",
				Typ: "JWT",
				Exp: **********,
			},
		},
		{
			name:   "empty_header",
			header: map[string]any{},
			want:   JwtDataHeader{},
		},
		{
			name: "wrong_types_ignored",
			header: map[string]any{
				"alg":    123,         // Should be string
				"exp":    "invalid",   // Should be int
				"client": true,        // Should be string
				"kid":    "valid-kid", // Valid string
			},
			want: JwtDataHeader{
				Kid: "valid-kid", // Only valid field should be set
			},
		},
		{
			name: "nil_values_ignored",
			header: map[string]any{
				"alg":    nil,
				"client": "test-client",
				"kid":    nil,
				"typ":    "JWT",
			},
			want: JwtDataHeader{
				Client: "test-client",
				Typ:    "JWT",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := mapToJwtDataHeader(tt.header)
			if got != tt.want {
				t.Errorf("mapToJwtDataHeader() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapToJwtDataPayload(t *testing.T) {
	tests := []struct {
		name    string
		payload map[string]any
		want    JwtDataPayload
	}{
		{
			name: "complete_payload_data",
			payload: map[string]any{
				"email":          "<EMAIL>",
				"email_verified": "true",
				"family_name":    "Doe",
				"given_name":     "John",
				"iss":            "https://cognito.amazonaws.com",
				"sub":            "12345678-1234-1234-1234-**********12",
				"username":       "johndoe",
				"exp":            **********,
				"identities":     `[{"userId":"12345","providerName":"Cognito","providerType":"SAML","issuer":"https://example.com","primary":"true","dateCreated":"**********"}]`,
			},
			want: JwtDataPayload{
				Email:         "<EMAIL>",
				EmailVerified: "true",
				FamilyName:    "Doe",
				GivenName:     "John",
				ISS:           "https://cognito.amazonaws.com",
				Sub:           "12345678-1234-1234-1234-**********12",
				Username:      "johndoe",
				Exp:           **********,
				Identities:    `[{"userId":"12345","providerName":"Cognito","providerType":"SAML","issuer":"https://example.com","primary":"true","dateCreated":"**********"}]`,
			},
		},
		{
			name: "partial_payload_data",
			payload: map[string]any{
				"email":    "<EMAIL>",
				"username": "johndoe",
				"exp":      **********,
			},
			want: JwtDataPayload{
				Email:    "<EMAIL>",
				Username: "johndoe",
				Exp:      **********,
			},
		},
		{
			name:    "empty_payload",
			payload: map[string]any{},
			want:    JwtDataPayload{},
		},
		{
			name: "wrong_types_ignored",
			payload: map[string]any{
				"email":       123,          // Should be string
				"exp":         "invalid",    // Should be int
				"given_name":  true,         // Should be string
				"family_name": "valid-name", // Valid string
			},
			want: JwtDataPayload{
				FamilyName: "valid-name", // Only valid field should be set
			},
		},
		{
			name: "nil_values_ignored",
			payload: map[string]any{
				"email":      nil,
				"username":   "test-user",
				"given_name": nil,
				"iss":        "https://cognito.amazonaws.com",
			},
			want: JwtDataPayload{
				Username: "test-user",
				ISS:      "https://cognito.amazonaws.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := mapToJwtDataPayload(tt.payload)
			if got != tt.want {
				t.Errorf("mapToJwtDataPayload() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewRBACPolicy(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
		verify  func(t *testing.T, policy *RBACPolicy)
	}{
		{
			name:    "successful_policy_creation",
			wantErr: false,
			verify: func(t *testing.T, policy *RBACPolicy) {
				if policy == nil {
					t.Error("expected policy to be created, got nil")
					return
				}
				if policy.Scopes == nil {
					t.Error("expected Scopes to be initialized")
				}
				if policy.Roles == nil {
					t.Error("expected Roles to be initialized")
				}

				// Verify some expected roles exist
				expectedRoles := []string{"admin", "guest", "enterprise-architect", "business-architect", "application-architect"}
				for _, role := range expectedRoles {
					if _, exists := policy.Roles[role]; !exists {
						t.Errorf("expected role %s to exist in policy", role)
					}
				}

				// Verify some expected scopes exist
				expectedScopes := []string{"all"}
				for _, scope := range expectedScopes {
					if _, exists := policy.Scopes[scope]; !exists {
						t.Errorf("expected scope %s to exist in policy", scope)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock injector (not used in the function but required by signature)
			injector := &do.Injector{}

			policy, err := NewRBACPolicy(injector)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewRBACPolicy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, policy)
			}
		})
	}
}

func TestRegister(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
		verify  func(t *testing.T, injector *do.Injector)
	}{
		{
			name:    "successful_registration",
			wantErr: false,
			verify: func(t *testing.T, injector *do.Injector) {
				// Verify that the RBACPolicy can be resolved from the injector
				policy, err := do.Invoke[*RBACPolicy](injector)
				if err != nil {
					t.Errorf("failed to resolve RBACPolicy from injector: %v", err)
					return
				}
				if policy == nil {
					t.Error("expected RBACPolicy to be resolved, got nil")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			injector := do.New()

			err := Register(injector)
			if (err != nil) != tt.wantErr {
				t.Errorf("Register() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, injector)
			}
		})
	}
}
