package policies

import (
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"path"
	"strings"

	"sa-intranet/core"

	"github.com/samber/do"
)

// RBAC error definitions for role-based access control operations
var (
	// ErrActionNotAllowed is returned when a requested action is not permitted
	ErrActionNotAllowed = errors.New("action not allowed")
	// ErrScopeNotAllowed is returned when access to a scope is denied
	ErrScopeNotAllowed = errors.New("scope not allowed")
	// ErrScopeNotFound is returned when a referenced scope doesn't exist in the policy
	ErrScopeNotFound = errors.New("scope not found in policy")
	// ErrActionNotAllowedScope is returned when an action is not allowed within a specific scope
	ErrActionNotAllowedScope = errors.New("action not allowed in scope")
	// ErrScopesRequired is returned when scopes are required but none are provided
	ErrScopesRequired = errors.New("scopes required but none provided")
	// ErrNoActionsProvided is returned when no actions are specified for evaluation
	ErrNoActionsProvided = errors.New("no actions provided")
	// ErrRoleNotFound is returned when a referenced role doesn't exist
	ErrRoleNotFound = errors.New("role not found")
	// ErrInvalidRuleEffect is returned when a rule has an invalid effect value
	ErrInvalidRuleEffect = errors.New("rule effect must be either 'allow' or 'deny'")
	// ErrAccessDenied is returned when access is explicitly denied by a rule
	ErrAccessDenied = errors.New("access denied by rule")
	// ErrNoMatchingRule is returned when no rule matches the requested action/scope
	ErrNoMatchingRule = errors.New("no matching rule found")
)

// rbacJSON contains the embedded RBAC policy configuration
//
//go:embed rbac.json
var rbacJSON []byte

// Scope represents a security scope with associated actions and optional filters.
// Scopes define the boundaries of what resources or operations can be accessed.
type Scope struct {
	// Description provides human-readable information about the scope
	Description string `json:"description"`
	// Filter contains optional filtering criteria for the scope
	Filter *string `json:"filter"`
	// Actions lists the permitted actions within this scope
	Actions []string `json:"actions"`
}

// Rule defines authorization rules that specify which actions are allowed
// within specific scopes and the effect of the rule (allow/deny).
type Rule struct {
	// Actions lists the actions this rule applies to
	Actions []string `json:"actions"`
	// AllowedScopes lists the scopes where these actions are permitted
	AllowedScopes []string `json:"allowed_scopes"`
	// AuthEffect specifies whether the rule allows or denies access ("allow" or "deny")
	AuthEffect string `json:"auth_effect"`
}

// Role represents a user role with associated authorization rules.
// Roles group related permissions and can be assigned to users.
type Role struct {
	// Description provides human-readable information about the role
	Description string `json:"description"`
	// Rules maps rule names to their definitions for this role
	Rules map[string]*Rule `json:"rules"`
}

// RBACPolicy represents the complete role-based access control policy configuration.
// It contains all scopes and roles defined in the system for authorization decisions.
type RBACPolicy struct {
	// Scopes maps scope names to their definitions
	Scopes map[string]*Scope `json:"scopes"`
	// Roles maps role names to their definitions
	Roles map[string]*Role `json:"roles"`
}

// NewRBACPolicy creates a new RBAC policy instance by loading and parsing
// the embedded RBAC JSON configuration. This function is used with dependency injection
// to provide a singleton RBAC policy throughout the application.
//
// Parameters:
//   - i: Dependency injection container (unused but required for DI pattern)
//
// Returns a configured RBACPolicy instance or an error if parsing fails.
func NewRBACPolicy(i *do.Injector) (*RBACPolicy, error) {
	var policy RBACPolicy
	if err := json.Unmarshal(rbacJSON, &policy); err != nil {
		return nil, fmt.Errorf("failed to unmarshal RBAC policy: %w", err)
	}

	return &policy, nil
}

// User represents a system user with role and department information
// used for authorization decisions.
type User struct {
	// ID is the unique user identifier
	ID string `json:"id"`
	// DepartmentID identifies the user's department for scope filtering
	DepartmentID string `json:"department_id"`
	// Role specifies the user's role for permission evaluation
	Role string `json:"role"`
}

// AuthRequest represents an authorization request containing all necessary
// information to make an access control decision.
type AuthRequest struct {
	// TenantID identifies the tenant context for multi-tenant applications
	TenantID string `json:"tenant_id"`
	// Service identifies the service being accessed
	Service string `json:"service"`
	// Path specifies the resource path being accessed
	Path          string   `json:"path"`
	Actions       []string `json:"actions"`
	AllowedScopes []string `json:"allowed_scopes,omitempty"`
	User          User     `json:"user"`
}

// ValidationResult contains the validation result and role information
type ValidationResult struct {
	Role          *Role             `json:"role"`
	Rule          *Rule             `json:"rule"`
	RuleKey       string            `json:"rule_key"`
	ActiveScopes  map[string]*Scope `json:"active_scopes"`
	ActiveActions []string          `json:"active_actions"`
}
type ValidateOptions struct {
	DisableScopesValidation bool
}

func (p *RBACPolicy) validateActions(rule *Rule, actions []string, result *ValidationResult) error {
	for _, action := range actions {
		actionAllowed := false

		for _, allowedAction := range rule.Actions {
			if action == allowedAction {
				actionAllowed = true

				result.ActiveActions = append(result.ActiveActions, action)

				break
			}
		}

		if !actionAllowed {
			return fmt.Errorf(core.ErrFormatWithString, ErrActionNotAllowed, action)
		}
	}

	return nil
}

func (p *RBACPolicy) validateScope(scope string, rule *Rule, actions []string) (*Scope, error) {
	scopeAllowed := false

	for _, allowedScope := range rule.AllowedScopes {
		if scope == allowedScope {
			scopeAllowed = true
			break
		}
	}

	if !scopeAllowed {
		return nil, fmt.Errorf(core.ErrFormatWithString, ErrScopeNotAllowed, scope)
	}

	scopeObj, exists := p.Scopes[scope]
	if !exists {
		return nil, fmt.Errorf(core.ErrFormatWithString, ErrScopeNotFound, scope)
	}

	for _, action := range actions {
		actionAllowed := false

		for _, scopeAction := range scopeObj.Actions {
			if action == scopeAction {
				actionAllowed = true
				break
			}
		}

		if !actionAllowed {
			return nil, fmt.Errorf("%w: %s in scope %s", ErrActionNotAllowedScope, action, scope)
		}
	}

	return scopeObj, nil
}

func (p *RBACPolicy) validateScopes(rule *Rule, req *AuthRequest, result *ValidationResult) error {
	if len(rule.AllowedScopes) == 0 {
		return nil
	}

	if len(req.AllowedScopes) == 0 {
		return ErrScopesRequired
	}

	for _, reqScope := range req.AllowedScopes {
		scope, err := p.validateScope(reqScope, rule, req.Actions)
		if err != nil {
			return err
		}

		result.ActiveScopes[reqScope] = scope
	}

	return nil
}

func (p *RBACPolicy) Validate(req *AuthRequest, opts ValidateOptions) (*ValidationResult, error) {
	result := &ValidationResult{
		ActiveScopes:  make(map[string]*Scope),
		ActiveActions: make([]string, 0, len(req.Actions)),
	}

	if len(req.Actions) == 0 {
		return nil, ErrNoActionsProvided
	}

	effectiveRole := "guest"
	if req.User.Role != "" {
		effectiveRole = req.User.Role
	}

	role, exists := p.Roles[effectiveRole]
	if !exists {
		return nil, fmt.Errorf(core.ErrFormatWithString, ErrRoleNotFound, effectiveRole)
	}

	result.Role = role

	if len(role.Rules) == 0 {
		slog.Warn("Role has no rules defined", "role", effectiveRole)
	}

	if err := p.checkDenyRules(role, req); err != nil {
		return nil, err
	}

	rule, ruleKey, err := p.findAllowRule(role, req)
	if err != nil {
		return nil, err
	}

	result.Rule = rule
	result.RuleKey = ruleKey

	if rule.AuthEffect != "allow" && rule.AuthEffect != "deny" {
		return nil, fmt.Errorf("%w: got '%s'", ErrInvalidRuleEffect, rule.AuthEffect)
	}

	if err := p.validateActions(rule, req.Actions, result); err != nil {
		return nil, err
	}

	if !opts.DisableScopesValidation {
		if err := p.validateScopes(rule, req, result); err != nil {
			return nil, err
		}
	}

	return result, nil
}

// checkDenyRules checks if any deny rules match the request path
// Returns an error if a deny rule matches, nil otherwise
func (p *RBACPolicy) checkDenyRules(role *Role, req *AuthRequest) error {
	for k, r := range role.Rules {
		if r.AuthEffect != "deny" {
			continue
		}

		parts := strings.Split(k, ":")
		if len(parts) != 3 {
			continue
		}

		// Check if tenant and service match
		if parts[0] == req.TenantID && parts[1] == req.Service {
			resourcePath := parts[2]

			matched, err := matchesPathPattern(resourcePath, req.Path)
			if err != nil {
				slog.Warn("Error matching path pattern", "error", err, "pattern", resourcePath, "path", req.Path)
				continue
			}

			if matched {
				return fmt.Errorf(core.ErrFormatWithString, ErrAccessDenied, k)
			}
		}
	}

	return nil
}

// findAllowRule looks for a matching allow rule for the given request
// Returns the matching rule and its key, or an error if no rule is found
func (p *RBACPolicy) findAllowRule(role *Role, req *AuthRequest) (*Rule, string, error) {
	// Build rule key
	ruleKey := fmt.Sprintf("%s:%s:%s", req.TenantID, req.Service, req.Path)

	// Check for exact match first
	if rule, exists := role.Rules[ruleKey]; exists {
		return rule, ruleKey, nil
	}

	// Check for wildcard matches in rule keys
	for k, r := range role.Rules {
		if r.AuthEffect != "allow" {
			continue
		}

		parts := strings.Split(k, ":")
		if len(parts) != 3 {
			continue
		}

		// Check if tenant and service match
		if parts[0] == req.TenantID && parts[1] == req.Service {
			resourcePath := parts[2]

			matched, err := matchesPathPattern(resourcePath, req.Path)
			if err != nil {
				slog.Warn("Error matching path pattern", "error", err, "pattern", resourcePath, "path", req.Path)
				continue
			}

			if matched {
				return r, k, nil
			}
		}
	}

	return nil, "", fmt.Errorf("%w for %s", ErrNoMatchingRule, ruleKey)
}

// matchesPathPattern checks if a request path matches a rule path pattern
func matchesPathPattern(rulePath, requestPath string) (bool, error) {
	// Ensure path starts with /
	if !strings.HasPrefix(rulePath, "/") {
		rulePath = "/" + rulePath
	}

	// Check for simple wildcard patterns first
	if rulePath == "/*" || rulePath == "*" {
		return true, nil
	}

	// Check for patterns like /admin* (without slash before *)
	if strings.HasSuffix(rulePath, "*") && !strings.HasSuffix(rulePath, "/*") {
		prefix := strings.TrimSuffix(rulePath, "*")
		if requestPath == prefix || strings.HasPrefix(requestPath, prefix+"/") {
			return true, nil
		}
	}

	// Check for patterns like /admin/* (with slash before *)
	if strings.HasSuffix(rulePath, "/*") {
		prefix := strings.TrimSuffix(rulePath, "/*")
		if strings.HasPrefix(requestPath, prefix+"/") {
			return true, nil
		}
	}

	// Fallback to path.Match for other patterns
	var globPattern string
	if strings.HasSuffix(rulePath, "/*") {
		globPattern = strings.ReplaceAll(rulePath, "/*", "/**")
	} else {
		globPattern = rulePath
	}

	return path.Match(globPattern, requestPath)
}
