package out

import (
	"time"

	"sa-intranet/core/viewmodel"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/service/in"

	date "sa-intranet/core/date"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type ListTokensItemViewModel struct {
	ID        uuid.UUID `json:"id"`
	Token     string    `json:"token"`
	ExpiresAt date.Date `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
}

type ListTokensViewModel struct {
	Items      []ListTokensItemViewModel `json:"data"`
	Pagination in.Pagination             `json:"pagination"`
	Roles      []Role                    `json:"roles"`
}

type ListTokenOutPort interface {
	Present(resp in.ListTokensOutput) viewmodel.Response[ListTokensViewModel]
	PresentError(err error) viewmodel.Response[ListTokensViewModel]
}

type ListTokenPresenter struct {
	rbac *policies.RBACPolicy
}

var _ ListTokenOutPort = (*ListTokenPresenter)(nil)

func (p *ListTokenPresenter) Present(resp in.ListTokensOutput) viewmodel.Response[ListTokensViewModel] {
	listViewModel := mapListTokensOutputToViewModel(resp)
	listViewModel.Pagination = resp.Pagination

	return viewmodel.Response[ListTokensViewModel]{
		Success: true,
		Data:    listViewModel,
	}
}

func (p *ListTokenPresenter) PresentError(err error) viewmodel.Response[ListTokensViewModel] {
	return viewmodel.Response[ListTokensViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewListTokenPresenter(i *do.Injector) (ListTokenOutPort, error) {
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &ListTokenPresenter{
		rbac: rbac,
	}, nil
}

func mapListTokensOutputToViewModel(resp in.ListTokensOutput) ListTokensViewModel {
	var viewModel ListTokensViewModel
	for _, token := range resp.Items {
		viewModel.Items = append(viewModel.Items, ListTokensItemViewModel(token))
	}

	return viewModel
}
