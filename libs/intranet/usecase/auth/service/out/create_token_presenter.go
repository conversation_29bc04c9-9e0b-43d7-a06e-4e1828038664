package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"
	"time"

	date "sa-intranet/core/date"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateTokenViewModel struct {
	ID        uuid.UUID `json:"id"`
	Token     string    `json:"token"`
	ExpiresAt date.Date `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
}

type CreateTokenOutPort interface {
	Present(resp in.CreateTokenOutput) viewmodel.Response[CreateTokenViewModel]
	PresentError(err error) viewmodel.Response[CreateTokenViewModel]
}

type CreateTokenPresenter struct{}

var _ CreateTokenOutPort = (*CreateTokenPresenter)(nil)

func (p *CreateTokenPresenter) Present(resp in.CreateTokenOutput) viewmodel.Response[CreateTokenViewModel] {
	return viewmodel.Response[CreateTokenViewModel]{
		Success: true,
		Data:    CreateTokenViewModel(resp),
	}
}

func (p *CreateTokenPresenter) PresentError(err error) viewmodel.Response[CreateTokenViewModel] {
	return viewmodel.Response[CreateTokenViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewCreateTokenPresenter(i *do.Injector) (CreateTokenOutPort, error) {
	return &CreateTokenPresenter{}, nil
}
