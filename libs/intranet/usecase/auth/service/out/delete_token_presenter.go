package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type DeleteTokenViewModel struct {
	ID uuid.UUID `json:"id"`
}

type DeleteTokenOutPort interface {
	Present(resp in.DeleteTokenOutput) viewmodel.Response[DeleteTokenViewModel]
	PresentError(err error) viewmodel.Response[DeleteTokenViewModel]
}

type DeleteTokenPresenter struct{}

var _ DeleteTokenOutPort = (*DeleteTokenPresenter)(nil)

func (p *DeleteTokenPresenter) Present(resp in.DeleteTokenOutput) viewmodel.Response[DeleteTokenViewModel] {
	return viewmodel.Response[DeleteTokenViewModel]{
		Success: true,
		Data:    DeleteTokenViewModel(resp),
	}
}

func (p *DeleteTokenPresenter) PresentError(err error) viewmodel.Response[DeleteTokenViewModel] {
	return viewmodel.Response[DeleteTokenViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewDeleteTokenPresenter(i *do.Injector) (DeleteTokenOutPort, error) {
	return &DeleteTokenPresenter{}, nil
}
