// Package out provides output ports (presenters) for the authentication system.
// These presenters handle response formatting and error presentation
// following Clean Architecture principles.
package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// CreateUserViewModel defines the view model structure for user creation responses.
// It represents the data that will be sent to the client after successful user creation.
type CreateUserViewModel struct {
	// ID is the unique identifier of the created user
	ID uuid.UUID `json:"id"`
	// Username is the user's login identifier
	Username string `json:"username"`
	// Email is the user's email address
	Email string `json:"email"`
	// Role is the user's assigned role
	Role string `json:"role"`
}

// CreateUserOutPort defines the interface for user creation response formatting.
// It represents the output port in Clean Architecture for user creation operations.
type CreateUserOutPort interface {
	// Present formats successful user creation responses
	Present(resp in.CreateUserOutput) viewmodel.Response[CreateUserViewModel]
	// PresentError formats error responses for user creation failures
	PresentError(err error) viewmodel.Response[CreateUserViewModel]
}

// CreateUserPresenter implements response formatting for user creation operations.
// It converts business logic outputs into standardized API responses.
type CreateUserPresenter struct{}

// Compile-time check to ensure CreateUserPresenter implements CreateUserOutPort interface
var _ CreateUserOutPort = (*CreateUserPresenter)(nil)

// Present formats a successful user creation response into a standardized view model.
// It converts the business logic output into a client-friendly response format.
func (p *CreateUserPresenter) Present(resp in.CreateUserOutput) viewmodel.Response[CreateUserViewModel] {
	return viewmodel.Response[CreateUserViewModel]{
		Success: true,
		Data:    CreateUserViewModel(resp),
	}
}

// PresentError formats error responses for user creation failures.
// It converts errors into standardized error responses with appropriate error codes.
func (p *CreateUserPresenter) PresentError(err error) viewmodel.Response[CreateUserViewModel] {
	return viewmodel.Response[CreateUserViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422", // Unprocessable Entity for validation errors
			},
		},
	}
}

// NewCreateUserPresenter creates a new CreateUserPresenter instance with dependency injection.
// It provides a factory function for the dependency injection container.
//
// Parameters:
//   - i: Dependency injection container (unused but required for DI pattern)
//
// Returns a CreateUserOutPort implementation.
func NewCreateUserPresenter(i *do.Injector) (CreateUserOutPort, error) {
	return &CreateUserPresenter{}, nil
}
