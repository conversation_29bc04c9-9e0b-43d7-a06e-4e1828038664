package service_test

import (
	"testing"

	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateUserTestCase struct {
	name    string
	input   in.CreateUserInput
	wantErr bool
	verify  func(t *testing.T, input in.CreateUserInput, output viewmodel.Response[out.CreateUserViewModel])
	setup   func(t *testing.T, input in.CreateUserInput)
}

func TestCreateUser(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)

	tests := []CreateUserTestCase{
		{
			name: "empty_input",
			input: in.CreateUserInput{
				Username: "",
				Email:    "",
				Role:     "",
			},
			wantErr: true,
		},
		{
			name: "invalid_role",
			input: in.CreateUserInput{
				Username: "testusername",
				Email:    "<EMAIL>",
				Role:     "test_role",
			},
			wantErr: true,
		},
		{
			name: "success",
			input: in.CreateUserInput{
				Username: "testusername_success",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			verify: func(t *testing.T, input in.CreateUserInput, output viewmodel.Response[out.CreateUserViewModel]) {
				if output.Data.Username != input.Username {
					t.Errorf("got username %v, want %v", output.Data.Username, input.Username)
				}
				if output.Data.Email != input.Email {
					t.Errorf("got email %v, want %v", output.Data.Email, input.Email)
				}
				if output.Data.Role != input.Role {
					t.Errorf("got role %v, want %v", output.Data.Role, input.Role)
				}
			},
		},
		{
			name: "duplicate_username",
			input: in.CreateUserInput{
				Username: "testusernameexample",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: true,
			setup: func(t *testing.T, input in.CreateUserInput) {
				t.Helper()
				// Create a user with the same username
				createOutput := svc.CreateUser(input)
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t, tt.input)
			}

			output := svc.CreateUser(tt.input)
			if (!output.Success) != tt.wantErr {
				t.Errorf("CreateUser() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

type UpdateUserTestCase struct {
	name    string
	input   in.UpdateUserInput
	wantErr bool
	verify  func(t *testing.T, input in.UpdateUserInput, output viewmodel.Response[out.UpdateUserViewModel])
	setup   func(t *testing.T) uuid.UUID
}

func TestUpdateUser(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)
	tests := []UpdateUserTestCase{
		{
			name: "empty_input",
			input: in.UpdateUserInput{
				Username: "",
				Email:    "",
				Role:     "",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "invalid_role",
			input: in.UpdateUserInput{
				Username: "updatedusername_invalid_role",
				Email:    "<EMAIL>",
				Role:     "invalid_role",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username_invalid_role",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "non_existent_user",
			input: in.UpdateUserInput{
				Username: "updatedusername",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()

				// Return a random UUID that doesn't exist
				return uuid.New()
			},
		},
		{
			name: "success",
			input: in.UpdateUserInput{
				Username: "updatedusername_success",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()

				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username_success",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			verify: func(t *testing.T, input in.UpdateUserInput, output viewmodel.Response[out.UpdateUserViewModel]) {
				if output.Data.Username != input.Username {
					t.Errorf("got username %v, want %v", output.Data.Username, input.Username)
				}
				if output.Data.Email != input.Email {
					t.Errorf("got email %v, want %v", output.Data.Email, input.Email)
				}
				if output.Data.Role != input.Role {
					t.Errorf("got role %v, want %v", output.Data.Role, input.Role)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup test data and get user ID
			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = uuid.New() // Default to a random ID if no setup
			}

			// Call the service method
			output := svc.UpdateUser(userID, tt.input)

			// Check if error status matches expectation
			if (!output.Success) != tt.wantErr {
				t.Errorf("UpdateUser() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			// Verify output if test expects success and has a verify function
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

type ListUsersTestCase struct {
	name     string
	input    in.ListUsersInput
	page     int
	pageSize int
	wantErr  bool
	setup    func(t *testing.T)
	verify   func(t *testing.T, input in.ListUsersInput, output viewmodel.Response[out.ListUsersViewModel])
}

func TestListUsers(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)

	tests := []ListUsersTestCase{
		{
			name: "success - list all users",
			input: in.ListUsersInput{
				Email: "",
			},
			page:     1,
			pageSize: 10,
			wantErr:  false,
			setup: func(t *testing.T) {
				// Create test users
				svc.CreateUser(in.CreateUserInput{
					Username: "listuser1",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				svc.CreateUser(in.CreateUserInput{
					Username: "listuser2",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
			},
			verify: func(t *testing.T, input in.ListUsersInput, output viewmodel.Response[out.ListUsersViewModel]) {
				if !output.Success {
					t.Errorf("Expected success but got errors: %v", output.Errors)
					return
				}
				if len(output.Data.Items) < 2 {
					t.Errorf("Expected at least 2 users, got %d", len(output.Data.Items))
				}
				if output.Data.Pagination.CurrentPage != 1 {
					t.Errorf("Expected current page 1, got %d", output.Data.Pagination.CurrentPage)
				}
				if output.Data.Pagination.PerPage != 10 {
					t.Errorf("Expected per page 10, got %d", output.Data.Pagination.PerPage)
				}
			},
		},
		{
			name: "success - filter by email",
			input: in.ListUsersInput{
				Email: "filtertest",
			},
			page:     1,
			pageSize: 10,
			wantErr:  false,
			setup: func(t *testing.T) {
				// Create test user with specific email
				svc.CreateUser(in.CreateUserInput{
					Username: "filteruser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
			},
			verify: func(t *testing.T, input in.ListUsersInput, output viewmodel.Response[out.ListUsersViewModel]) {
				if !output.Success {
					t.Errorf("Expected success but got errors: %v", output.Errors)
					return
				}
				// Check that returned users contain the filter email
				found := false
				for _, user := range output.Data.Items {
					if user.Email == "<EMAIL>" {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("Expected to find user with email containing '%s'", input.Email)
				}
			},
		},
		{
			name: "success - pagination with page 0 defaults to 1",
			input: in.ListUsersInput{
				Email: "",
			},
			page:     0, // Should default to 1
			pageSize: 5,
			wantErr:  false,
			verify: func(t *testing.T, input in.ListUsersInput, output viewmodel.Response[out.ListUsersViewModel]) {
				if !output.Success {
					t.Errorf("Expected success but got errors: %v", output.Errors)
					return
				}
				if output.Data.Pagination.CurrentPage != 1 {
					t.Errorf("Expected current page 1 (default), got %d", output.Data.Pagination.CurrentPage)
				}
				if output.Data.Pagination.PerPage != 5 {
					t.Errorf("Expected per page 5, got %d", output.Data.Pagination.PerPage)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t)
			}

			output := svc.ListUsers(tt.input, tt.page, tt.pageSize)

			if (!output.Success) != tt.wantErr {
				t.Errorf("ListUsers() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

type UpdateUserRoleTestCase struct {
	name    string
	input   in.UpdateUserRoleInput
	wantErr bool
	setup   func(t *testing.T) uuid.UUID
	verify  func(t *testing.T, input in.UpdateUserRoleInput, output viewmodel.Response[out.UpdateUserRoleViewModel])
}

func TestUpdateUserRole(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)

	tests := []UpdateUserRoleTestCase{
		{
			name: "success - update user role",
			input: in.UpdateUserRoleInput{
				Role: "admin",
			},
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				// Create a user to update role
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "roleuser_success",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			verify: func(t *testing.T, input in.UpdateUserRoleInput, output viewmodel.Response[out.UpdateUserRoleViewModel]) {
				if !output.Success {
					t.Errorf("Expected success but got errors: %v", output.Errors)
					return
				}
				if output.Data.RoleID != input.Role {
					t.Errorf("Expected role %s, got %s", input.Role, output.Data.RoleID)
				}
				if output.Data.UserID == uuid.Nil {
					t.Errorf("Expected non-nil user ID")
				}
				if output.Data.UserEmail == "" {
					t.Errorf("Expected non-empty user email")
				}
			},
		},
		{
			name: "error - empty role",
			input: in.UpdateUserRoleInput{
				Role: "",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "roleuser_empty",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "error - invalid role",
			input: in.UpdateUserRoleInput{
				Role: "invalid_role_name",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "roleuser_invalid",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "error - non-existent user",
			input: in.UpdateUserRoleInput{
				Role: "guest",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				// Return a random UUID that doesn't exist
				return uuid.New()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup test data and get user ID
			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = uuid.New() // Default to a random ID if no setup
			}

			// Call the service method
			output := svc.UpdateUserRole(userID, tt.input)

			// Check if error status matches expectation
			if (!output.Success) != tt.wantErr {
				t.Errorf("UpdateUserRole() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			// Verify output if test expects success and has a verify function
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}
