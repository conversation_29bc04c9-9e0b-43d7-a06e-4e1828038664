package service

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/samber/do"
)

type TokenService struct {
	createTokenInPort  in.CreateTokenInPort
	createTokenOutPort out.CreateTokenOutPort
	listTokenInPort    in.ListTokenInPort
	listTokenOutPort   out.ListTokenOutPort
	deleteTokenInPort  in.DeleteTokenInPort
	deleteTokenOutPort out.DeleteTokenOutPort
}

func NewTokenService(i *do.Injector) (*TokenService, error) {
	createTokenInPort := do.MustInvoke[in.CreateTokenInPort](i)
	createTokenOutPort := do.MustInvoke[out.CreateTokenOutPort](i)
	listTokenInPort := do.MustInvoke[in.ListTokenInPort](i)
	listTokenOutPort := do.MustInvoke[out.ListTokenOutPort](i)
	deleteTokenInPort := do.MustInvoke[in.DeleteTokenInPort](i)
	deleteTokenOutPort := do.MustInvoke[out.DeleteTokenOutPort](i)

	return &TokenService{
		createTokenInPort:  createTokenInPort,
		createTokenOutPort: createTokenOutPort,
		listTokenInPort:    listTokenInPort,
		listTokenOutPort:   listTokenOutPort,
		deleteTokenInPort:  deleteTokenInPort,
		deleteTokenOutPort: deleteTokenOutPort,
	}, nil
}

func (s *TokenService) CreateToken(input in.CreateTokenInput) viewmodel.Response[out.CreateTokenViewModel] {
	var viewmodel viewmodel.Response[out.CreateTokenViewModel]

	output, err := s.createTokenInPort.CreateToken(input)
	if err != nil {
		return s.createTokenOutPort.PresentError(err)
	}

	viewmodel = s.createTokenOutPort.Present(output)

	return viewmodel
}

func (s *TokenService) ListTokens(input in.ListTokensInput, page int, pageSize int) viewmodel.Response[out.ListTokensViewModel] {
	var viewmodel viewmodel.Response[out.ListTokensViewModel]

	output, err := s.listTokenInPort.ListToken(input, page, pageSize)
	if err != nil {
		return s.listTokenOutPort.PresentError(err)
	}

	viewmodel = s.listTokenOutPort.Present(output)

	return viewmodel
}

func (s *TokenService) DeleteToken(input in.DeleteTokenInput) viewmodel.Response[out.DeleteTokenViewModel] {
	var viewmodel viewmodel.Response[out.DeleteTokenViewModel]

	output, err := s.deleteTokenInPort.DeleteUserToken(input)
	if err != nil {
		return s.deleteTokenOutPort.PresentError(err)
	}

	viewmodel = s.deleteTokenOutPort.Present(output)

	return viewmodel
}
