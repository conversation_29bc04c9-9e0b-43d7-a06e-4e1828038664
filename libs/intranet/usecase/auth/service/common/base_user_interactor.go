// Package common provides shared functionality and base structures for authentication interactors.
// It contains common operations and utilities that are used across multiple user management interactors,
// promoting code reuse and consistency in the authentication system.
package common

import (
	"context"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// BaseUserInteractor provides common functionality and dependencies for user-related interactors.
// It serves as a base struct that can be embedded in specific interactors to share
// common operations like validation, repository access, and RBAC policy enforcement.
type BaseUserInteractor struct {
	// Repo provides access to user data persistence operations
	Repo repository.UserRepository
	// Validate provides input validation capabilities
	Validate *validator.Validate
	// Rbac provides role-based access control policy enforcement
	Rbac *policies.RBACPolicy
}

// FindUser retrieves a user by their unique ID from the repository.
// This is a common operation used by multiple interactors for user lookup.
//
// Parameters:
//   - ctx: Context for the database operation
//   - id: Unique identifier of the user to retrieve
//
// Returns the user model or an error if the user is not found or database operation fails.
func (b *BaseUserInteractor) FindUser(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return b.Repo.Find(ctx, id)
}

// SaveUser persists a user model to the repository with configurable save behavior.
// It provides a common interface for both creating new users and updating existing ones.
//
// Parameters:
//   - ctx: Context for the database operation
//   - user: The user model to save
//   - isNew: Flag indicating whether this is a new user (insert) or existing user (update)
//
// Returns the saved user model or an error if the save operation fails.
func (b *BaseUserInteractor) SaveUser(ctx context.Context, user *model.User, isNew bool) (*model.User, error) {
	opts := []corerepo.SaveOptionsFunc{
		corerepo.WithSaveNew(isNew),
	}

	return b.Repo.Save(ctx, user, opts...)
}
