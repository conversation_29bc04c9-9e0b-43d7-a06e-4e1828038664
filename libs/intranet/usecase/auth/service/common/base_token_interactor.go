package common

import (
	"context"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// BaseTokenInteractor contains common functionality for token interactors
type BaseTokenInteractor struct {
	Repo         repository.TokenRepository
	Validate     *validator.Validate
	JWTSecretKey string
}

// FindToken retrieves a token by ID
func (b *BaseTokenInteractor) FindToken(ctx context.Context, id uuid.UUID) (*model.Token, error) {
	return b.Repo.Find(ctx, id)
}

// SaveToken saves a token to the repository
func (b *BaseTokenInteractor) SaveToken(ctx context.Context, token *model.Token, isNew bool) (*model.Token, error) {
	opts := []corerepo.SaveOptionsFunc{
		corerepo.WithSaveNew(isNew),
	}

	return b.Repo.Save(ctx, token, opts...)
}

// DeleteToken deletes a token by ID
func (b *BaseTokenInteractor) DeleteToken(ctx context.Context, id uuid.UUID) error {
	return b.Repo.Delete(ctx, id)
}
