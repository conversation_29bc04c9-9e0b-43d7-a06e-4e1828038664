package common

import (
	"errors"
	"fmt"

	"sa-intranet/policies"

	"github.com/go-playground/validator/v10"
)

// Validation error definitions for user operations
var (
	// ErrEmptyRole is returned when a role field is empty or not provided
	ErrEmptyRole = errors.New("role cannot be empty")
	// ErrRoleNotExists is returned when a role is not defined in the RBAC policy
	ErrRoleNotExists = errors.New("role does not exist in the RBAC policy")
)

// ValidateRole checks if a role exists in the RBAC policy configuration.
// It ensures that only valid, predefined roles can be assigned to users,
// maintaining consistency with the authorization system.
//
// Parameters:
//   - rbac: The RBAC policy containing valid role definitions
//   - role: The role string to validate
//
// Returns an error if the role is empty or not found in the RBAC policy.
func ValidateRole(rbac *policies.RBACPolicy, role string) error {
	if role == "" {
		return ErrEmptyRole
	}

	if rbac != nil && role != "" {
		if _, exists := rbac.Roles[role]; !exists {
			return fmt.Errorf("%w: '%s'", ErrRoleNotExists, role)
		}
	}

	return nil
}

// ValidateUserInput performs comprehensive validation of user input data.
// It combines struct validation using validator tags with business rule validation
// such as role existence in the RBAC policy.
//
// Parameters:
//   - validate: The validator instance for struct validation
//   - input: The input struct to validate (must have validation tags)
//   - rbac: The RBAC policy for role validation
//   - role: The role to validate against the RBAC policy
//
// Returns an error if validation fails at any level (struct validation or business rules).
func ValidateUserInput(validate *validator.Validate, input any, rbac *policies.RBACPolicy, role string) error {
	// Validate that the role exists in the RBAC policy
	if err := ValidateRole(rbac, role); err != nil {
		return err
	}

	// Validate struct using validator tags
	if err := validate.Struct(input); err != nil {
		return err
	}

	return nil
}
