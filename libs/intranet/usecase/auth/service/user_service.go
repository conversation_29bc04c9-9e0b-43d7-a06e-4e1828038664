// Package service provides the main orchestration layer for user authentication operations.
// It implements the Service layer in Clean Architecture, coordinating between input ports (interactors)
// and output ports (presenters) to handle complete user management workflows.
package service

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// UserService orchestrates user management operations by coordinating input and output ports.
// It follows Clean Architecture principles by separating business logic (interactors)
// from presentation logic (presenters) while providing a unified interface for user operations.
type UserService struct {
	// Input ports (interactors) for business logic
	createUserInPort     in.CreateUserInPort
	updateUserInPort     in.UpdateUserInPort
	listUserInPort       in.ListUserInPort
	updateUserRoleInPort in.UpdateUserRoleInPort

	// Output ports (presenters) for response formatting
	createUserOutPort     out.CreateUserOutPort
	updateUserOutPort     out.UpdateUserOutPort
	listUserOutPort       out.ListUserOutPort
	updateUserRoleOutPort out.UpdateUserRoleOutPort
}

// NewUserService creates a new UserService instance with all required dependencies injected.
// It retrieves all input and output ports from the dependency injection container
// and wires them together to create a complete user management service.
//
// Parameters:
//   - i: Dependency injection container containing all required ports
//
// Returns a configured UserService instance or an error if dependency injection fails.
func NewUserService(i *do.Injector) (*UserService, error) {
	// Inject input ports (interactors) for business logic
	createUserInPort := do.MustInvoke[in.CreateUserInPort](i)
	updateUserInPort := do.MustInvoke[in.UpdateUserInPort](i)
	listUserInPort := do.MustInvoke[in.ListUserInPort](i)
	updateUserRoleInPort := do.MustInvoke[in.UpdateUserRoleInPort](i)

	// Inject output ports (presenters) for response formatting
	createUserOutPort := do.MustInvoke[out.CreateUserOutPort](i)
	updateUserOutPort := do.MustInvoke[out.UpdateUserOutPort](i)
	listUserOutPort := do.MustInvoke[out.ListUserOutPort](i)
	updateUserRoleOutPort := do.MustInvoke[out.UpdateUserRoleOutPort](i)

	return &UserService{
		createUserInPort:      createUserInPort,
		createUserOutPort:     createUserOutPort,
		updateUserInPort:      updateUserInPort,
		updateUserOutPort:     updateUserOutPort,
		listUserInPort:        listUserInPort,
		listUserOutPort:       listUserOutPort,
		updateUserRoleInPort:  updateUserRoleInPort,
		updateUserRoleOutPort: updateUserRoleOutPort,
	}, nil
}

func (s *UserService) CreateUser(input in.CreateUserInput) viewmodel.Response[out.CreateUserViewModel] {
	var viewmodel viewmodel.Response[out.CreateUserViewModel]

	output, err := s.createUserInPort.CreateUser(input)
	if err != nil {
		return s.createUserOutPort.PresentError(err)
	}

	viewmodel = s.createUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) UpdateUser(id uuid.UUID, input in.UpdateUserInput) viewmodel.Response[out.UpdateUserViewModel] {
	var viewmodel viewmodel.Response[out.UpdateUserViewModel]

	output, err := s.updateUserInPort.UpdateUser(id, input)
	if err != nil {
		return s.updateUserOutPort.PresentError(err)
	}

	viewmodel = s.updateUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) ListUsers(input in.ListUsersInput, page int, pageSize int) viewmodel.Response[out.ListUsersViewModel] {
	var viewmodel viewmodel.Response[out.ListUsersViewModel]

	output, err := s.listUserInPort.ListUser(input, page, pageSize)
	if err != nil {
		return s.listUserOutPort.PresentError(err)
	}

	viewmodel = s.listUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) UpdateUserRole(id uuid.UUID, input in.UpdateUserRoleInput) viewmodel.Response[out.UpdateUserRoleViewModel] {
	var viewmodel viewmodel.Response[out.UpdateUserRoleViewModel]

	output, err := s.updateUserRoleInPort.UpdateUserRole(id, input)
	if err != nil {
		return s.updateUserRoleOutPort.PresentError(err)
	}

	viewmodel = s.updateUserRoleOutPort.Present(output)

	return viewmodel
}
