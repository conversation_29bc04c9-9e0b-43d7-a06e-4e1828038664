package in

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"sa-intranet/core/cache"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// UserDevelopmentProvisioningInteractor handles user provisioning from JWT data
type UserDevelopmentProvisioningInteractor struct {
	userRepo repository.UserRepository
	cache    cache.Cache
	// Default cache expiration time
	cacheExpiration time.Duration
	defaultRole     string
}

// NewUserDevelopmentProvisioningService creates a new user provisioning service
func NewUserDevelopmentProvisioningService(i *do.Injector) (*UserDevelopmentProvisioningInteractor, error) {
	userRepo := do.MustInvoke[repository.UserRepository](i)
	cache := do.MustInvoke[cache.Cache](i)
	authConfig := do.MustInvoke[config.AuthConfig](i)

	return &UserDevelopmentProvisioningInteractor{
		userRepo:        userRepo,
		cache:           cache,
		cacheExpiration: 60 * time.Minute, // Default cache expiration
		defaultRole:     authConfig.DefaultRole,
	}, nil
}

// GetDevelopmentUser retrieves a user from JWT data or creates one if it doesn't exist
func (s *UserDevelopmentProvisioningInteractor) GetDevelopmentUser(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	// Generate a cache key based on a unique identifier from JWT
	cacheKey := "development_user_" + userID.String()

	// Try to get from cache first
	cachedData, err := s.cache.Get(ctx, cacheKey)
	if err == nil {
		var user model.User
		// Deserialize the cached user
		if unmarshalErr := json.Unmarshal(cachedData, &user); unmarshalErr == nil {
			return &user, nil
		}
	}

	// Try to find user by email using List method with filter
	user, err := s.userRepo.Find(ctx, userID)
	if err == nil && user != nil {
		// Serialize and cache the user
		userData, marshalErr := json.Marshal(user)
		if marshalErr == nil {
			if setErr := s.cache.Set(ctx, cacheKey, userData, s.cacheExpiration); setErr != nil {
				// Log the error but continue
				log.Printf("Failed to cache user: %v", setErr)
			}
		}

		return user, nil
	}
	// If user not found, return an error

	return nil, err
}
