package in

import (
	"context"
	"encoding/json"
	"log"
	"strings"
	"time"

	"sa-intranet/core/cache"
	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// UserProvisioningInteractor handles automatic user provisioning from JWT authentication data.
// It provides functionality to create users on-the-fly during authentication if they don't exist,
// enabling seamless integration with external identity providers like AWS Cognito.
type UserProvisioningInteractor struct {
	// userRepo provides access to user data persistence
	userRepo repository.UserRepository
	// cache provides temporary storage for user data to improve performance
	cache cache.Cache
	// cacheExpiration defines how long user data is cached
	cacheExpiration time.Duration
	// defaultRole is assigned to newly provisioned users
	defaultRole string
}

// NewUserProvisioningService creates a new user provisioning service with dependency injection.
// It initializes the service with all required dependencies for automatic user creation
// and caching capabilities.
//
// Parameters:
//   - i: Dependency injection container containing required services
//
// Returns a configured UserProvisioningInteractor instance or an error if injection fails.
func NewUserProvisioningService(i *do.Injector) (*UserProvisioningInteractor, error) {
	userRepo := do.MustInvoke[repository.UserRepository](i)
	cache := do.MustInvoke[cache.Cache](i)
	authConfig := do.MustInvoke[config.AuthConfig](i)

	return &UserProvisioningInteractor{
		userRepo:        userRepo,
		cache:           cache,
		cacheExpiration: 60 * time.Minute, // Cache user data for 1 hour
		defaultRole:     authConfig.DefaultRole,
	}, nil
}

// GetOrCreateUserFromJWT retrieves an existing user or creates a new one based on JWT authentication data.
// This method implements automatic user provisioning by extracting user information from JWT tokens
// and either finding the existing user or creating a new one with default settings.
//
// The method uses caching to improve performance and reduce database queries for frequently
// authenticated users. It follows a cache-aside pattern with automatic cache population.
//
// Parameters:
//   - ctx: Context for the operation
//   - jwtData: JWT authentication data containing user information
//
// Returns the user model (existing or newly created) or an error if the operation fails.
func (s *UserProvisioningInteractor) GetOrCreateUserFromJWT(ctx context.Context, jwtData policies.JwtData) (*model.User, error) {
	jwtPayload := jwtData.Payload
	// Generate a cache key based on the user's email from JWT
	cacheKey := "user_jwt_" + jwtPayload.Email

	// Attempt to retrieve user data from cache first for performance
	cachedData, err := s.cache.Get(ctx, cacheKey)
	if err == nil {
		// Deserialize the cached user
		var user model.User
		if unmarshalErr := json.Unmarshal(cachedData, &user); unmarshalErr == nil {
			return &user, nil
		}
	}

	// Try to find user by email using List method with filter
	params := corerepo.PaginationParams[repository.UserFilter]{
		Page:     1,
		PageSize: 1,
		Filters: repository.UserFilter{
			Email: jwtPayload.Email,
		},
	}

	result, err := s.userRepo.List(ctx, params)
	if err == nil && len(result.Items) > 0 {
		user := &result.Items[0]

		// Serialize and cache the user
		userData, marshalErr := json.Marshal(user)
		if marshalErr == nil {
			if setErr := s.cache.Set(ctx, cacheKey, userData, s.cacheExpiration); setErr != nil {
				// Log the error but continue
				log.Printf("Failed to cache user: %v", setErr)
			}
		}

		return user, nil
	}

	// User not found, create a new one with default role
	newUser := &model.User{
		ID:        uuid.New(),
		Username:  jwtPayload.Username,
		Email:     jwtPayload.Email,
		FirstName: strings.TrimSpace(jwtPayload.GivenName),
		LastName:  strings.TrimSpace(jwtPayload.FamilyName),
		Role:      s.defaultRole, // Default role
	}

	// Save the new user
	savedUser, err := s.userRepo.Save(ctx, newUser)
	if err != nil {
		return nil, err
	}

	// Serialize and cache the new user
	userData, err := json.Marshal(savedUser)
	if err == nil {
		if setErr := s.cache.Set(ctx, cacheKey, userData, s.cacheExpiration); setErr != nil {
			// Log the error but continue
			log.Printf("Failed to cache user: %v", setErr)
		}
	}

	return savedUser, nil
}
