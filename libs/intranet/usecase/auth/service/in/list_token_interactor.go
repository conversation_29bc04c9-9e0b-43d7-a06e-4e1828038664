package in

import (
	"context"
	"time"

	coreRepo "sa-intranet/core/repository"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"

	date "sa-intranet/core/date"
)

type ListTokensInput struct {
	UserID uuid.UUID `json:"user_id,omitempty"`
}

type TokenListItem struct {
	ID        uuid.UUID `json:"id"`
	Token     string    `json:"token"`
	ExpiresAt date.Date `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
}

type ListTokensOutput struct {
	Items      []TokenListItem `json:"data"`
	Pagination Pagination      `json:"pagination"`
}

type ListTokenInPort interface {
	ListToken(input ListTokensInput, page int, pageSize int) (ListTokensOutput, error)
}

type ListTokenInteractor struct {
	common.BaseTokenInteractor
}

func NewListTokenInteractor(i *do.Injector) (ListTokenInPort, error) {
	repo := do.MustInvoke[repository.TokenRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())

	return &ListTokenInteractor{
		BaseTokenInteractor: common.BaseTokenInteractor{
			Repo:     repo,
			Validate: validate,
		},
	}, nil
}

func (i *ListTokenInteractor) ListToken(input ListTokensInput, page int, pageSize int) (ListTokensOutput, error) {
	var output ListTokensOutput

	if page == 0 {
		page = 1
	}

	ctx := context.Background()
	params := coreRepo.PaginationParams[repository.TokenFilter]{
		Filters: repository.TokenFilter{
			UserID: input.UserID,
		},
		Page:     page,
		PageSize: pageSize,
	}

	result, err := i.Repo.List(ctx, params)
	if err != nil {
		return output, err
	}

	for _, token := range result.Items {
		output.Items = append(output.Items, TokenListItem{
			ID:        token.ID,
			Token:     token.Token,
			ExpiresAt: date.Date{Time: token.ExpiresAt},
			CreatedAt: token.CreatedAt,
		})
	}

	output.Pagination = Pagination{
		TotalCount:  int(result.TotalItems),
		CurrentPage: result.Page,
		PerPage:     result.PageSize,
	}

	return output, nil
}
