package in_test

import (
	"context"
	"testing"

	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type UserDevelopmentProvisioningTestCase struct {
	name    string
	userID  uuid.UUID
	setup   func(t *testing.T) uuid.UUID
	wantErr bool
	verify  func(t *testing.T, user *model.User)
}

func TestUserDevelopmentProvisioningInteractor_GetDevelopmentUser(t *testing.T) {
	t.Parallel()

	interactor := do.MustInvoke[*in.UserDevelopmentProvisioningInteractor](injector)
	UserService := do.MustInvoke[*service.UserService](injector)

	tests := []UserDevelopmentProvisioningTestCase{
		{
			name:    "user_not_found",
			wantErr: true,
		},
		{
			name:    "user_found",
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Simulate user creation in cache or database
				user := UserService.CreateUser(in.CreateUserInput{
					Username: "devuser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !user.Success {
					t.Fatalf("Failed to create test user: %v", user.Errors)
				}
				return user.Data.ID
			},
			verify: func(t *testing.T, user *model.User) {
				if user == nil {
					t.Errorf("expected user, got nil")
				}
				if user.Email != "<EMAIL>" {
					t.Errorf("<NAME_EMAIL>, got %v", user.Email)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if tt.setup != nil {
				tt.userID = tt.setup(t)
			} else {
				tt.userID = uuid.New()
			}
			user, err := interactor.GetDevelopmentUser(context.Background(), tt.userID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDevelopmentUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, user)
			}
		})
	}
}
