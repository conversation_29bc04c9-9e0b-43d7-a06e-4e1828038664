package in_test

import (
	"context"
	"testing"

	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	ctx := context.Background()
	injector = do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx), // Apply migrations, needs to be after WithPostgres
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	// Run all tests
	m.Run()
}

const (
	validationFailedMsg = "required"
	roleEmptyMsg        = "role cannot be empty"
	roleNotExistsMsg    = "role does not exist in the RBAC policy"
	usernameExistsMsg   = "username_already_exists"
	notFoundMsg         = "sql: no rows in result set"
	testEmail           = "<EMAIL>"
	guestRole           = "guest"
	expectedErrorMsg    = "Expected error but got none"
	unexpectedErrorMsg  = "Unexpected error: %v"
)

func TestCreateUserInteractorCreateUser(t *testing.T) {
	tests := []struct {
		name        string
		input       in.CreateUserInput
		wantErr     bool
		errContains string
		verify      func(t *testing.T, got in.CreateUserOutput, input in.CreateUserInput)
	}{
		{
			name: "success case - valid user creation",
			input: in.CreateUserInput{
				Username: "testuser_create_success",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			verify: func(t *testing.T, got in.CreateUserOutput, input in.CreateUserInput) {
				assert.NotEqual(t, uuid.Nil, got.ID, "Expected non-nil UUID")
				assert.Equal(t, input.Username, got.Username, "Username should match input")
				assert.Equal(t, input.Email, got.Email, "Email should match input")
				assert.Equal(t, input.Role, got.Role, "Role should match input")
			},
		},
		{
			name: "error case - empty username",
			input: in.CreateUserInput{
				Username: "",
				Email:    testEmail,
				Role:     guestRole,
			},
			wantErr:     true,
			errContains: validationFailedMsg,
		},
		{
			name: "error case - empty email",
			input: in.CreateUserInput{
				Username: "testuser",
				Email:    "",
				Role:     guestRole,
			},
			wantErr:     true,
			errContains: validationFailedMsg,
		},
		{
			name: "error case - empty role",
			input: in.CreateUserInput{
				Username: "testuser",
				Email:    testEmail,
				Role:     "",
			},
			wantErr:     true,
			errContains: roleEmptyMsg,
		},
		{
			name: "error case - invalid role",
			input: in.CreateUserInput{
				Username: "testuser_invalid_role",
				Email:    "<EMAIL>",
				Role:     "invalid_role_name",
			},
			wantErr:     true,
			errContains: roleNotExistsMsg,
		},
		{
			name: "error case - duplicate username",
			input: in.CreateUserInput{
				Username: "duplicate_user",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr:     true,
			errContains: usernameExistsMsg,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup: Create duplicate user if needed
			if tt.name == "error case - duplicate username" {
				interactor := do.MustInvoke[in.CreateUserInPort](injector)
				_, _ = interactor.CreateUser(tt.input) // Create first user
			}

			interactor := do.MustInvoke[in.CreateUserInPort](injector)
			got, err := interactor.CreateUser(tt.input)

			if tt.wantErr {
				require.Error(t, err, expectedErrorMsg)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				}
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestUpdateUserInteractorUpdateUser(t *testing.T) {
	tests := []struct {
		name        string
		userID      uuid.UUID
		input       in.UpdateUserInput
		wantErr     bool
		errContains string
		setup       func(t *testing.T) uuid.UUID
		verify      func(t *testing.T, got in.UpdateUserOutput, input in.UpdateUserInput)
	}{
		{
			name: "success case - valid user update",
			input: in.UpdateUserInput{
				Username: "updated_username",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				// Create a user to update
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "original_user_update",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				require.NoError(t, err)
				return created.ID
			},
			verify: func(t *testing.T, got in.UpdateUserOutput, input in.UpdateUserInput) {
				assert.Equal(t, input.Username, got.Username, "Username should match input")
				assert.Equal(t, input.Email, got.Email, "Email should match input")
				assert.Equal(t, input.Role, got.Role, "Role should match input")
			},
		},
		{
			name: "error case - empty username",
			input: in.UpdateUserInput{
				Username: "",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr:     true,
			errContains: validationFailedMsg,
			setup: func(t *testing.T) uuid.UUID {
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "user_for_empty_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				require.NoError(t, err)
				return created.ID
			},
		},
		{
			name: "error case - invalid role",
			input: in.UpdateUserInput{
				Username: "valid_username",
				Email:    "<EMAIL>",
				Role:     "invalid_role",
			},
			wantErr:     true,
			errContains: roleNotExistsMsg,
			setup: func(t *testing.T) uuid.UUID {
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "user_for_invalid_role",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				require.NoError(t, err)
				return created.ID
			},
		},
		{
			name:   "error case - non-existent user",
			userID: uuid.New(), // Random UUID that doesn't exist
			input: in.UpdateUserInput{
				Username: "valid_username",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr:     true,
			errContains: notFoundMsg,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = tt.userID
			}

			interactor := do.MustInvoke[in.UpdateUserInPort](injector)
			got, err := interactor.UpdateUser(userID, tt.input)

			if tt.wantErr {
				require.Error(t, err, expectedErrorMsg)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				}
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestListUserInteractorListUser(t *testing.T) {
	tests := []struct {
		name     string
		input    in.ListUsersInput
		page     int
		pageSize int
		wantErr  bool
		setup    func(t *testing.T)
		verify   func(t *testing.T, got in.ListUsersOutput, input in.ListUsersInput)
	}{
		{
			name: "success case - list all users",
			input: in.ListUsersInput{
				Email: "",
			},
			page:     1,
			pageSize: 10,
			wantErr:  false,
			setup: func(t *testing.T) {
				// Create test users
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				_, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "listuser1",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
				_, err = createInteractor.CreateUser(in.CreateUserInput{
					Username: "listuser2",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
			},
			verify: func(t *testing.T, got in.ListUsersOutput, input in.ListUsersInput) {
				assert.GreaterOrEqual(t, len(got.Items), 2, "Should have at least 2 users")
				assert.Greater(t, got.Pagination.TotalCount, 0, "Total count should be greater than 0")
				assert.Equal(t, 1, got.Pagination.CurrentPage, "Current page should be 1")
				assert.Equal(t, 10, got.Pagination.PerPage, "Per page should be 10")
			},
		},
		{
			name: "success case - filter by email",
			input: in.ListUsersInput{
				Email: "filtertest",
			},
			page:     1,
			pageSize: 10,
			wantErr:  false,
			setup: func(t *testing.T) {
				// Create test user with specific email
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				_, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "filteruser",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
			},
			verify: func(t *testing.T, got in.ListUsersOutput, input in.ListUsersInput) {
				assert.GreaterOrEqual(t, len(got.Items), 1, "Should have at least 1 user")
				// Check that returned users contain the filter email
				for _, user := range got.Items {
					assert.Contains(t, user.Email, input.Email, "User email should contain filter")
				}
			},
		},
		{
			name: "success case - pagination with page 0 defaults to 1",
			input: in.ListUsersInput{
				Email: "",
			},
			page:     0, // Should default to 1
			pageSize: 5,
			wantErr:  false,
			verify: func(t *testing.T, got in.ListUsersOutput, input in.ListUsersInput) {
				assert.Equal(t, 1, got.Pagination.CurrentPage, "Page 0 should default to 1")
				assert.Equal(t, 5, got.Pagination.PerPage, "Per page should be 5")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t)
			}

			interactor := do.MustInvoke[in.ListUserInPort](injector)
			got, err := interactor.ListUser(tt.input, tt.page, tt.pageSize)

			if tt.wantErr {
				require.Error(t, err, expectedErrorMsg)
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestUpdateUserRoleInteractorUpdateUserRole(t *testing.T) {
	tests := []struct {
		name        string
		userID      uuid.UUID
		input       in.UpdateUserRoleInput
		wantErr     bool
		errContains string
		setup       func(t *testing.T) uuid.UUID
		verify      func(t *testing.T, got in.UpdateUserRoleOutput, input in.UpdateUserRoleInput)
	}{
		{
			name: "success case - valid role update",
			input: in.UpdateUserRoleInput{
				Role: "admin",
			},
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				// Create a user to update role
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "roleuser_success",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
				return created.ID
			},
			verify: func(t *testing.T, got in.UpdateUserRoleOutput, input in.UpdateUserRoleInput) {
				assert.Equal(t, input.Role, got.RoleID, "Role should match input")
				assert.NotEqual(t, uuid.Nil, got.UserID, "UserID should not be nil")
				assert.NotEmpty(t, got.UserEmail, "UserEmail should not be empty")
			},
		},
		{
			name: "error case - empty role",
			input: in.UpdateUserRoleInput{
				Role: "",
			},
			wantErr:     true,
			errContains: roleEmptyMsg,
			setup: func(t *testing.T) uuid.UUID {
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "roleuser_empty",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
				return created.ID
			},
		},
		{
			name: "error case - invalid role",
			input: in.UpdateUserRoleInput{
				Role: "invalid_role_name",
			},
			wantErr:     true,
			errContains: roleNotExistsMsg,
			setup: func(t *testing.T) uuid.UUID {
				createInteractor := do.MustInvoke[in.CreateUserInPort](injector)
				created, err := createInteractor.CreateUser(in.CreateUserInput{
					Username: "roleuser_invalid",
					Email:    "<EMAIL>",
					Role:     guestRole,
				})
				require.NoError(t, err)
				return created.ID
			},
		},
		{
			name:   "error case - non-existent user",
			userID: uuid.New(), // Random UUID that doesn't exist
			input: in.UpdateUserRoleInput{
				Role: guestRole,
			},
			wantErr:     true,
			errContains: notFoundMsg,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = tt.userID
			}

			interactor := do.MustInvoke[in.UpdateUserRoleInPort](injector)
			got, err := interactor.UpdateUserRole(userID, tt.input)

			if tt.wantErr {
				require.Error(t, err, expectedErrorMsg)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
				}
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}
