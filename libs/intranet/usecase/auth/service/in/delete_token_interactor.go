package in

import (
	"context"
	"errors"
	"fmt"

	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type DeleteTokenInput struct {
	UserID uuid.UUID `json:"userId" validate:"required"`
	ID     uuid.UUID `json:"id" validate:"required"`
}

type DeleteTokenOutput struct {
	ID uuid.UUID `json:"id"`
}

type DeleteTokenInPort interface {
	DeleteUserToken(input DeleteTokenInput) (DeleteTokenOutput, error)
}

type DeleteTokenInteractor struct {
	common.BaseTokenInteractor
}

var (
	ErrTokenNotBelongToUser = errors.New("token does not belong to user")
)

func NewDeleteTokenInteractor(i *do.Injector) (DeleteTokenInPort, error) {
	repo := do.MustInvoke[repository.TokenRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())

	return &DeleteTokenInteractor{
		BaseTokenInteractor: common.BaseTokenInteractor{
			Repo:     repo,
			Validate: validate,
		},
	}, nil
}

func (i *DeleteTokenInteractor) DeleteUserToken(input DeleteTokenInput) (DeleteTokenOutput, error) {
	var output DeleteTokenOutput

	// Use common validation
	err := common.ValidateTokenInput(i.Validate, input)
	if err != nil {
		return output, err
	}

	tokenModel, err := i.FindToken(context.Background(), input.ID)
	if err != nil {
		return output, err
	}

	// Check if token belongs to user
	if tokenModel.UserID != input.UserID {
		return output, fmt.Errorf("%w: user %s", ErrTokenNotBelongToUser, input.UserID)
	}

	// Delete the token using base interactor
	err = i.DeleteToken(context.Background(), tokenModel.ID)
	if err != nil {
		return output, err
	}

	// Map the output
	output = i.mapTokenToOutput(input.ID)

	return output, nil
}

func (i *DeleteTokenInteractor) mapTokenToOutput(id uuid.UUID) DeleteTokenOutput {
	return DeleteTokenOutput{
		ID: id,
	}
}
