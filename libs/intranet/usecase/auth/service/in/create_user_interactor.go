// Package in provides input ports (interactors) for the authentication system.
// These interactors implement business logic for user management operations
// following Clean Architecture principles.
package in

import (
	"context"

	"sa-intranet/core/validatorext"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// CreateUserInput defines the input data structure for creating a new user.
// It includes validation rules to ensure data integrity and uniqueness constraints.
type CreateUserInput struct {
	// Username must be unique and is validated against existing users
	Username string `json:"username" validate:"required,username_already_exists"`
	// Email is required for user identification and communication
	Email string `json:"email" validate:"required"`
	// Role defines the user's permissions and access level
	Role string `json:"role" validate:"required"`
}

// CreateUserOutput defines the response data structure after successful user creation.
// It contains the essential user information including the generated ID.
type CreateUserOutput struct {
	// ID is the unique identifier assigned to the newly created user
	ID uuid.UUID `json:"id"`
	// Username is the user's login identifier
	Username string `json:"username"`
	// Email is the user's email address
	Email string `json:"email"`
	// Role is the user's assigned role for authorization
	Role string `json:"role"`
}

// CreateUserInPort defines the interface for user creation business logic.
// It represents the input port in Clean Architecture for user creation operations.
type CreateUserInPort interface {
	// CreateUser processes user creation with validation and business rules
	CreateUser(input CreateUserInput) (CreateUserOutput, error)
}

// CreateUserInteractor implements the business logic for creating new users.
// It extends BaseUserInteractor to inherit common functionality and validation.
type CreateUserInteractor struct {
	common.BaseUserInteractor
}

func NewCreateUserInteractor(i *do.Injector) (CreateUserInPort, error) {
	db := do.MustInvoke[*bun.DB](i)
	repo := do.MustInvoke[repository.UserRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())

	err := validate.RegisterValidationCtx("username_already_exists", validatorext.ValidateModelWithKeyAlredyExists[string](
		db,
		(*model.User)(nil),
		"username",
	))
	if err != nil {
		return nil, err
	}

	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &CreateUserInteractor{
		BaseUserInteractor: common.BaseUserInteractor{
			Repo:     repo,
			Validate: validate,
			Rbac:     rbac,
		},
	}, nil
}

func (i *CreateUserInteractor) CreateUser(input CreateUserInput) (CreateUserOutput, error) {
	var output CreateUserOutput

	// Use common validation
	err := common.ValidateUserInput(i.Validate, input, i.Rbac, input.Role)
	if err != nil {
		return output, err
	}

	userModel := i.mapInputToModel(input)

	// Use base interactor to save
	user, err := i.SaveUser(context.Background(), userModel, true)
	if err != nil {
		return output, err
	}

	output = i.mapUserToOutput(user)

	return output, nil
}

func (i *CreateUserInteractor) mapUserToOutput(user *model.User) CreateUserOutput {
	return CreateUserOutput{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
	}
}

func (i *CreateUserInteractor) mapInputToModel(input CreateUserInput) *model.User {
	return &model.User{
		Username: input.Username,
		Email:    input.Email,
		Role:     input.Role,
	}
}
