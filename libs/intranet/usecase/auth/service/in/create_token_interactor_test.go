package in_test

import (
	"testing"
	"time"

	"sa-intranet/core/date"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateTokenInteractorTestCase struct {
	name    string
	input   in.CreateTokenInput
	wantErr bool
	setup   func(t *testing.T) uuid.UUID
	verify  func(t *testing.T, input in.CreateTokenInput, output in.CreateTokenOutput)
}

func TestCreateTokenInteractor(t *testing.T) {
	t.Parallel()

	interactor := do.MustInvoke[in.CreateTokenInPort](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)
	authConfig := do.MustInvoke[config.AuthConfig](injector)
	jwtSecret := authConfig.JWTSecretKey

	tests := []CreateTokenInteractorTestCase{
		{
			name:    "empty_input",
			input:   in.CreateTokenInput{},
			wantErr: true,
		},
		{
			name: "success",
			input: in.CreateTokenInput{
				ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
			},
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "jwt_test_user",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			wantErr: false,
			verify: func(t *testing.T, input in.CreateTokenInput, output in.CreateTokenOutput) {
				if output.ExpiresAt.UTC().Format("2006-01-02") != input.ExpiresAt.Time.UTC().Format("2006-01-02") {
					t.Errorf("got expiresAt %v, want %v", output.ExpiresAt.UTC().Format("2006-01-02"), input.ExpiresAt.Time.UTC().Format("2006-01-02"))
				}
				if output.ID == uuid.Nil {
					t.Errorf("token ID is empty")
				}
				if output.Token == "" {
					t.Errorf("token string is empty")
				}
				if output.CreatedAt.IsZero() {
					t.Errorf("createdAt is empty")
				}

				// JWT content test
				parsed, err := jwt.Parse(output.Token, func(token *jwt.Token) (interface{}, error) {
					// Accept any signing method for test
					return []byte(jwtSecret), nil
				})
				if err != nil {
					t.Errorf("failed to parse JWT: %v", err)
					return
				}
				claims, ok := parsed.Claims.(jwt.MapClaims)
				if !ok {
					t.Errorf("JWT claims type assertion failed")
					return
				}
				if claims["sub"] != input.UserID.String() {
					t.Errorf("JWT sub claim = %v, want %v", claims["sub"], input.UserID.String())
				}
				if int64(claims["exp"].(float64)) != input.ExpiresAt.Unix() {
					t.Errorf("JWT exp claim = %v, want %v", claims["exp"], input.ExpiresAt.Unix())
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = uuid.New()
			}
			tt.input.UserID = userID

			output, err := interactor.CreateToken(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}
