package in_test

import (
	"testing"
	"time"

	"sa-intranet/core/date"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type DeleteTokenInteractorTestCase struct {
	name    string
	setup   func(t *testing.T) (uuid.UUID, uuid.UUID)
	wantErr bool
	verify  func(t *testing.T, err error)
}

func TestDeleteTokenInteractor_DeleteToken(t *testing.T) {
	t.Parallel()

	interactor := do.MustInvoke[in.DeleteTokenInPort](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)
	tokensvc := do.MustInvoke[*service.TokenService](injector)

	tests := []DeleteTokenInteractorTestCase{
		{
			name: "token_not_found",
			setup: func(t *testing.T) (uuid.UUID, uuid.UUID) {
				userOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "notfounduser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !userOutput.Success {
					t.Fatalf("Failed to create test user: %v", userOutput.Errors)
				}
				return userOutput.Data.ID, uuid.New() // random token ID
			},
			wantErr: true,
			verify: func(t *testing.T, err error) {
				if err == nil {
					t.Errorf("expected error for missing token, got nil")
				}
			},
		},
		{
			name: "token_deleted",
			setup: func(t *testing.T) (uuid.UUID, uuid.UUID) {
				userOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "deletetokenuser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !userOutput.Success {
					t.Fatalf("Failed to create test user: %v", userOutput.Errors)
				}
				userID := userOutput.Data.ID
				tokenOutput := tokensvc.CreateToken(in.CreateTokenInput{
					UserID:    userID,
					ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
				})
				if !tokenOutput.Success {
					t.Fatalf("Failed to create test token: %v", tokenOutput.Errors)
				}
				return userID, tokenOutput.Data.ID
			},
			wantErr: false,
			verify: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("expected no error, got %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			userID, tokenID := tt.setup(t)
			input := in.DeleteTokenInput{
				UserID: userID,
				ID:     tokenID,
			}
			deleteOutput, err := interactor.DeleteUserToken(input)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if deleteOutput.ID != tokenID && !tt.wantErr {
				t.Errorf("DeleteToken() ID = %v, want %v", deleteOutput.ID, tokenID)
			}
			if tt.verify != nil {
				tt.verify(t, err)
			}
		})
	}
}
