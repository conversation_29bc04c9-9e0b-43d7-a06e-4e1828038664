package in_test

import (
	"testing"
	"time"

	"sa-intranet/core/date"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type ListTokenInteractorTestCase struct {
	name    string
	setup   func(t *testing.T) uuid.UUID
	input   in.ListTokensInput
	wantErr bool
	verify  func(t *testing.T, tokens in.ListTokensOutput)
}

func TestListTokenInteractor_ListTokens(t *testing.T) {
	t.<PERSON>llel()

	interactor := do.MustInvoke[in.ListTokenInPort](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)
	tokensvc := do.MustInvoke[*service.TokenService](injector)

	tests := []ListTokenInteractorTestCase{
		{
			name: "no_tokens",
			input: in.ListTokensInput{
				UserID: uuid.New(),
			},
			setup: func(t *testing.T) uuid.UUID {
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "notokensuser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			wantErr: false,
			verify: func(t *testing.T, tokens in.ListTokensOutput) {
				if len(tokens.Items) != 0 {
					t.Errorf("expected 0 tokens, got %d", len(tokens.Items))
				}
			},
		},
		{
			name: "tokens_exist",
			input: in.ListTokensInput{
				UserID: uuid.New(),
			},
			setup: func(t *testing.T) uuid.UUID {
				usersvc := do.MustInvoke[*service.UserService](injector)
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "tokensuser",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				userID := createOutput.Data.ID

				tokenOutput := tokensvc.CreateToken(in.CreateTokenInput{
					UserID:    userID,
					ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
				})
				if !tokenOutput.Success {
					t.Fatalf("Failed to create test token: %v", tokenOutput.Errors)
				}
				return userID
			},
			wantErr: false,
			verify: func(t *testing.T, tokens in.ListTokensOutput) {
				if len(tokens.Items) == 0 {
					t.Errorf("expected tokens, got none")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			tt.input.UserID = tt.setup(t)
			tokens, err := interactor.ListToken(tt.input, 1, 10)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListTokens() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tokens)
			}
		})
	}
}
