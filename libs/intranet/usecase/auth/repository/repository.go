// Package repository defines data access interfaces and types for the authentication system.
// It provides abstractions for user data operations and filtering capabilities,
// following the Repository pattern from Clean Architecture.
package repository

import (
	"context"
	"errors"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"

	"github.com/google/uuid"
)

// Error definitions for repository operations
var (
	// ErrRecordNotFound is returned when a requested user record doesn't exist
	ErrRecordNotFound = errors.New("record not found")
	// ErrInvalidPage is returned when pagination parameters are invalid
	ErrInvalidPage = errors.New("invalid page parameters")
)

// UserFilter defines filtering criteria for user queries.
// It supports filtering by various user attributes for search and listing operations.
type UserFilter struct {
	// Username filters users by exact username match
	Username string `json:"username,omitempty"`
	// Email filters users by exact email match
	Email string `json:"email,omitempty"`
	// EmailCont filters users by partial email match (contains)
	EmailCont string `json:"email_cont,omitempty"`
	// Role filters users by their assigned role
	Role string `json:"role,omitempty"`
}

type TokenFilter struct {
	Expired string    `json:"expired,omitempty"`
	UserID  uuid.UUID `json:"user_id,omitempty"`
}

type UserRepository interface {
	// Find retrieves a single user by their unique ID
	Find(ctx context.Context, id uuid.UUID) (*model.User, error)
	// List retrieves a paginated list of users with optional filtering
	List(ctx context.Context, params repository.PaginationParams[UserFilter]) (*repository.PaginatedResult[model.User], error)
	// Save creates or updates a user record with configurable options
	Save(ctx context.Context, user *model.User, opts ...repository.SaveOptionsFunc) (*model.User, error)
}

type TokenRepository interface {
	Find(ctx context.Context, id uuid.UUID) (*model.Token, error)
	List(ctx context.Context, params repository.PaginationParams[TokenFilter]) (*repository.PaginatedResult[model.Token], error)
	Save(ctx context.Context, token *model.Token, opts ...repository.SaveOptionsFunc) (*model.Token, error)
	Delete(ctx context.Context, id uuid.UUID) error
}
