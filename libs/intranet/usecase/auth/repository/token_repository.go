package repository

import (
	"context"
	"sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"
	"time"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

type TokenDefaultRepository struct {
	DB bun.IDB
}

// compile time check if TokenDefaultRepository implements TokenRepository
var _ TokenRepository = (*TokenDefaultRepository)(nil)

func NewTokenDefaultRepository(i *do.Injector) (TokenRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &TokenDefaultRepository{
		DB: db,
	}, nil
}

func (r *TokenDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.Token, error) {
	record, err := repository.Find[model.Token](ctx, r.DB, id)
	return record, err
}

func (r *TokenDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[TokenFilter],
) (*repository.PaginatedResult[model.Token], error) {
	return repository.FindAll[model.Token](ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[TokenFilter]) {
				if params.Filters.UserID != uuid.Nil {
					q.Where("user_id = ?", params.Filters.UserID)
				}

				if params.Filters.Expired != "" {
					q.Where("expires_at < ?", time.Now())
				}
			}))
}

func (r *TokenDefaultRepository) Save(ctx context.Context, token *model.Token, opts ...repository.SaveOptionsFunc) (*model.Token, error) {
	err := repository.Save(ctx, r.DB, token, opts...)
	if err != nil {
		return nil, err
	}

	return token, nil
}

func (r *TokenDefaultRepository) Delete(ctx context.Context, id uuid.UUID) error {
	err := repository.Delete[model.Token](ctx, r.DB, id)
	if err != nil {
		return err
	}

	return nil
}
