package repository

import (
	"context"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// UserDefaultRepository is the default implementation of UserRepository using Bun ORM.
// It provides concrete implementations for all user data access operations.
type UserDefaultRepository struct {
	// DB is the database connection interface for executing queries
	DB bun.IDB
}

// Compile-time check to ensure UserDefaultRepository implements UserRepository interface
var _ UserRepository = (*UserDefaultRepository)(nil)

// NewUserDefaultRepository creates a new instance of UserDefaultRepository with dependency injection.
// It retrieves the database connection from the DI container and initializes the repository.
//
// Parameters:
//   - i: Dependency injection container containing the database connection
//
// Returns a UserRepository implementation or an error if database injection fails.
func NewUserDefaultRepository(i *do.Injector) (UserRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &UserDefaultRepository{
		DB: db,
	}, nil
}

// Find retrieves a single user by their unique ID using the generic repository Find function.
// It delegates to the core repository implementation for consistent behavior.
func (r *UserDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.User, error) {
	record, err := repository.Find[model.User](ctx, r.DB, id)
	return record, err
}

// List retrieves a paginated list of users with optional filtering capabilities.
// It supports filtering by username, email (exact and partial match), and role.
// The method uses the generic FindAll function with custom query building for filters.
//
// Parameters:
//   - ctx: Context for the database operation
//   - params: Pagination parameters including filters, page size, and page number
//
// Returns a paginated result containing users and metadata, or an error if the query fails.
func (r *UserDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[UserFilter],
) (*repository.PaginatedResult[model.User], error) {
	return repository.FindAll[model.User](ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[UserFilter]) {
				// Apply username filter for exact match
				if params.Filters.Username != "" {
					q.Where("username = ?", params.Filters.Username)
				}

				// Apply email filter for exact match
				if params.Filters.Email != "" {
					q.Where("email = ?", params.Filters.Email)
				}

				// Apply email filter for partial match (case-insensitive)
				if params.Filters.EmailCont != "" {
					q.Where("email ILIKE ?", "%"+params.Filters.EmailCont+"%")
				}
			}))
}

// Save creates or updates a user record in the database.
// It uses the generic repository Save function with configurable options for insert/update behavior.
//
// Parameters:
//   - ctx: Context for the database operation
//   - user: The user model to save
//   - opts: Optional configuration functions for save behavior
//
// Returns the saved user model or an error if the operation fails.
func (r *UserDefaultRepository) Save(ctx context.Context, user *model.User, opts ...repository.SaveOptionsFunc) (*model.User, error) {
	err := repository.Save(ctx, r.DB, user, opts...)
	if err != nil {
		return nil, err
	}

	return user, nil
}
