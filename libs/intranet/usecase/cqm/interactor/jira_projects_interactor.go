package interactor

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"sa-intranet/core"
	corerepository "sa-intranet/core/repository"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

// ErrValidationFailed is returned when input validation fails for Jira project operations
var ErrValidationFailed = errors.New("1 or more validation errors occurred")

// JiraProjectsData represents the response structure for Jira project listings.
// It includes both the project data and pagination metadata.
type JiraProjectsData struct {
	// JiraProjects contains the list of Jira projects for the current page
	JiraProjects []JiraProject `json:"jiraProjects"`
	// Pagination contains metadata about the current page and total count
	Pagination Pagination `json:"pagination"`
}

// JiraProject represents a Jira project configuration with integration details.
// It contains all necessary information for connecting to and managing Jira projects.
type JiraProject struct {
	// ID is the unique identifier for the Jira project configuration
	ID uuid.UUID `json:"id"`
	// Name is the human-readable project name
	Name string `json:"name"`
	// Username is the Jira username for authentication
	Username string `json:"username"`
	// Active indicates if the project integration is currently active
	Active bool `json:"active"`
	// JiraURL is the base URL for the Jira instance
	JiraURL string `json:"jiraURL"`
	// CreatedAt is the timestamp when the project was created
	CreatedAt time.Time `json:"createdAt"`
	// UpdatedAt is the timestamp when the project was last modified
	UpdatedAt time.Time `json:"updatedAt"`
	// ProjectKey is the Jira project key identifier
	ProjectKey string `json:"projectKey"`
	// IssueTypeID is the default issue type for creating issues
	IssueTypeID string `json:"issueTypeID"`
	// CompanyClient is the associated company client information
	CompanyClient *CompanyClient `json:"companyClient,omitempty"`
}

// JiraProjectsInteractor handles business logic for Jira project management.
// It provides operations for creating, updating, and managing Jira project integrations.
type JiraProjectsInteractor struct {
	// jiraClient provides access to Jira API operations
	jiraClient *jira.Client
	// JiraProjectRepo provides data access for Jira projects
	JiraProjectRepo repository.JiraProjectRepository
	// cypher provides encryption/decryption for sensitive data
	cypher core.Cypher
	// validator provides input validation capabilities
	validator *validator.Validate
}

type JiraProjectValidator struct {
	ProjectKey      string    `json:"projectKey" validate:"required"`
	Token           string    `json:"token" validate:"required"`
	Username        string    `json:"username" validate:"required,email"`
	JiraURL         string    `json:"jiraURL" validate:"required,url"`
	Active          *bool     `json:"active" validate:"required"`
	CompanyClientID uuid.UUID `json:"companyClientId" validate:"required,company_client_exists"`
}

type JiraProjectUpdateValidator struct {
	ProjectKey      string    `json:"projectKey" validate:"required"`
	Token           string    `json:"token"`
	Username        string    `json:"username" validate:"required,email"`
	Active          *bool     `json:"active" validate:"required"`
	JiraURL         string    `json:"jiraURL" validate:"required,url"`
	CompanyClientID uuid.UUID `json:"companyClientId" validate:"required,company_client_exists"`
}

// ValidationError represents a single validation error for a specific field
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func NewJiraProjectsInteractor(i *do.Injector) (*JiraProjectsInteractor, error) {
	jiraClient, err := do.Invoke[*jira.Client](i)
	if err != nil {
		return nil, err
	}

	JiraProjectRepo, err := do.Invoke[repository.JiraProjectRepository](i)
	if err != nil {
		return nil, err
	}

	cypher, err := do.Invoke[core.Cypher](i)
	if err != nil {
		return nil, err
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, err
	}

	return &JiraProjectsInteractor{
		jiraClient:      jiraClient,
		JiraProjectRepo: JiraProjectRepo,
		cypher:          cypher,
		validator:       validator,
	}, nil
}

func (i *JiraProjectsInteractor) InitialData(filter string, page int, pageSize int) (JiraProjectsData, error) {
	var data JiraProjectsData
	ctx := context.Background()

	params := corerepository.PaginationParams[repository.JiraProjectFilter]{
		Filters: repository.JiraProjectFilter{
			Name: filter,
		},
		Page:     page,
		PageSize: pageSize,
	}

	jiraProjectsResp, err := i.JiraProjectRepo.List(ctx, params)
	if err != nil {
		return data, err
	}

	jiraProjects := make([]JiraProject, 0, len(jiraProjectsResp.Items))

	for _, project := range jiraProjectsResp.Items {
		var companyClient *CompanyClient
		if project.CompanyClient != nil {
			companyClient = &CompanyClient{
				ID:   project.CompanyClient.ID,
				Name: project.CompanyClient.Name,
			}
		}

		jiraProjects = append(jiraProjects, JiraProject{
			ID:            project.ID,
			Name:          project.Name,
			JiraURL:       project.JiraURL,
			Active:        project.Active,
			CreatedAt:     project.CreatedAt,
			UpdatedAt:     project.UpdatedAt,
			ProjectKey:    project.ProjectKey,
			Username:      project.Username,
			CompanyClient: companyClient,
		})
	}

	pagination := Pagination{
		TotalCount:  int(jiraProjectsResp.TotalItems),
		CurrentPage: jiraProjectsResp.Page,
		PerPage:     jiraProjectsResp.PageSize,
	}

	return JiraProjectsData{
		Pagination:   pagination,
		JiraProjects: jiraProjects,
	}, nil
}

func (i *JiraProjectsInteractor) CreateJiraProject(jiraProject JiraProjectValidator) (JiraProject, map[string]any, error) {
	jiraProjectResp := JiraProject{}

	validationErrors, err := i.validateJiraProject(jiraProject)
	if err != nil {
		return jiraProjectResp, validationErrors, err
	}

	projectResp, err := i.fetchJiraProject(jiraProject)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	encryptedToken, err := i.cypher.Encrypt(jiraProject.Token)
	if err != nil {
		return jiraProjectResp, nil, fmt.Errorf("failed to encrypt token: %w", err)
	}

	project := i.buildJiraProjectModel(jiraProject, projectResp, encryptedToken)

	jiraProjectDB, err := i.saveAndRetrieveJiraProject(project)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	return i.buildJiraProjectResponse(jiraProjectDB), nil, nil
}

func (i *JiraProjectsInteractor) validateJiraProject(jiraProject JiraProjectValidator) (map[string]any, error) {
	err := i.validator.Struct(jiraProject)
	if err != nil {
		var validationErr validator.ValidationErrors
		ok := errors.As(err, &validationErr)

		if !ok {
			return nil, fmt.Errorf("failed to validate jira project: %w", err)
		}

		validationErrors := validatorext.FormatFormErrors(jiraProject, validationErr)

		return validationErrors, ErrValidationFailed
	}

	return nil, nil
}

func (i *JiraProjectsInteractor) fetchJiraProject(jiraProject JiraProjectValidator) (jira.Project, error) {
	var (
		projectResp    jira.Project
		projectRespErr error
		wg             sync.WaitGroup
	)

	creds := jira.Credentials{
		Username: jiraProject.Username,
		Token:    jiraProject.Token,
	}

	wg.Add(1)

	go func() {
		defer wg.Done()

		projectResp, projectRespErr = i.jiraClient.GetProject(creds, jiraProject.JiraURL, jiraProject.ProjectKey)
	}()

	wg.Wait()

	if projectRespErr != nil {
		return projectResp, fmt.Errorf("failed to get Jira project: %w", projectRespErr)
	}

	return projectResp, nil
}

func (i *JiraProjectsInteractor) buildJiraProjectModel(validator JiraProjectValidator, projectResp jira.Project, encryptedToken string) *model.JiraProject {
	return &model.JiraProject{
		ProjectID:       projectResp.ID,
		Name:            projectResp.Name,
		ProjectKey:      validator.ProjectKey,
		JiraURL:         validator.JiraURL,
		Username:        validator.Username,
		Active:          *validator.Active,
		Token:           encryptedToken,
		CompanyClientID: validator.CompanyClientID,
	}
}

func (i *JiraProjectsInteractor) saveAndRetrieveJiraProject(project *model.JiraProject) (*model.JiraProject, error) {
	ctx := context.Background()

	jiraProjectDB, saveErr := i.JiraProjectRepo.Save(ctx, project)
	if saveErr != nil {
		return nil, saveErr
	}

	return i.JiraProjectRepo.Find(ctx, jiraProjectDB.ID)
}

func (i *JiraProjectsInteractor) buildJiraProjectResponse(jiraProjectDB *model.JiraProject) JiraProject {
	var companyClient *CompanyClient
	if jiraProjectDB.CompanyClient != nil {
		companyClient = &CompanyClient{
			ID:   jiraProjectDB.CompanyClient.ID,
			Name: jiraProjectDB.CompanyClient.Name,
		}
	}

	return JiraProject{
		ID:            jiraProjectDB.ID,
		Name:          jiraProjectDB.Name,
		JiraURL:       jiraProjectDB.JiraURL,
		CreatedAt:     jiraProjectDB.CreatedAt,
		UpdatedAt:     jiraProjectDB.UpdatedAt,
		ProjectKey:    jiraProjectDB.ProjectKey,
		Username:      jiraProjectDB.Username,
		Active:        jiraProjectDB.Active,
		CompanyClient: companyClient,
	}
}

func (i *JiraProjectsInteractor) UpdateJiraProject(jiraProjectID string, jiraProject JiraProjectUpdateValidator) (JiraProject, map[string]any, error) {
	ctx := context.Background()
	jiraProjectResp := JiraProject{}

	_, jiraProjectFromDB, err := i.prepareUpdateData(ctx, jiraProjectID)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	validationErrors, err := i.validateUpdateRequest(jiraProject)
	if err != nil {
		return jiraProjectResp, validationErrors, err
	}

	token, err := i.resolveToken(jiraProject.Token, jiraProjectFromDB.Token)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	projectResp, err := i.fetchJiraProjectForUpdate(jiraProject, token)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	updatedProject, err := i.buildUpdatedJiraProject(jiraProjectFromDB, jiraProject, projectResp, token)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	return i.saveAndReturnUpdatedProject(ctx, updatedProject)
}

func (i *JiraProjectsInteractor) prepareUpdateData(ctx context.Context, jiraProjectID string) (uuid.UUID, *model.JiraProject, error) {
	projectUUID, err := uuid.Parse(jiraProjectID)
	if err != nil {
		return uuid.Nil, nil, err
	}

	jiraProjectFromDB, err := i.JiraProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return uuid.Nil, nil, err
	}

	return projectUUID, jiraProjectFromDB, nil
}

func (i *JiraProjectsInteractor) validateUpdateRequest(jiraProject JiraProjectUpdateValidator) (map[string]any, error) {
	err := i.validator.Struct(jiraProject)
	if err != nil {
		var validationErr validator.ValidationErrors
		ok := errors.As(err, &validationErr)

		if !ok {
			return nil, fmt.Errorf("failed to validate jira project: %w", err)
		}

		validationErrors := validatorext.FormatFormErrors(jiraProject, validationErr)

		return validationErrors, ErrValidationFailed
	}

	return nil, nil
}

func (i *JiraProjectsInteractor) resolveToken(newToken, existingEncryptedToken string) (string, error) {
	if newToken != "" {
		return newToken, nil
	}

	decryptedToken, err := i.cypher.Decrypt(existingEncryptedToken)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt token: %w", err)
	}

	return decryptedToken, nil
}

func (i *JiraProjectsInteractor) fetchJiraProjectForUpdate(jiraProject JiraProjectUpdateValidator, token string) (jira.Project, error) {
	var (
		projectResp    jira.Project
		projectRespErr error
		wg             sync.WaitGroup
	)

	creds := jira.Credentials{
		Username: jiraProject.Username,
		Token:    token,
	}

	wg.Add(1)

	go func() {
		defer wg.Done()

		projectResp, projectRespErr = i.jiraClient.GetProject(creds, jiraProject.JiraURL, jiraProject.ProjectKey)
	}()

	wg.Wait()

	if projectRespErr != nil {
		return projectResp, fmt.Errorf("failed to get Jira project: %w", projectRespErr)
	}

	return projectResp, nil
}

func (i *JiraProjectsInteractor) buildUpdatedJiraProject(
	existingProject *model.JiraProject,
	updateData JiraProjectUpdateValidator,
	projectResp jira.Project,
	token string,
) (*model.JiraProject, error) {
	encryptedToken, err := i.cypher.Encrypt(token)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt token: %w", err)
	}

	return &model.JiraProject{
		ID:        existingProject.ID,
		JiraURL:   updateData.JiraURL,
		Username:  updateData.Username,
		Active:    *updateData.Active,
		Token:     encryptedToken,
		Name:      projectResp.Name,
		ProjectID: projectResp.ID,

		ProjectKey:      updateData.ProjectKey,
		CompanyClientID: updateData.CompanyClientID,
		CompanyClient:   existingProject.CompanyClient,
		CreatedAt:       existingProject.CreatedAt,
		UpdatedAt:       existingProject.UpdatedAt,
	}, nil
}

func (i *JiraProjectsInteractor) saveAndReturnUpdatedProject(ctx context.Context, project *model.JiraProject) (JiraProject, map[string]any, error) {
	jiraProjectResp := JiraProject{}

	project, saveErr := i.JiraProjectRepo.Save(ctx, project)
	if saveErr != nil {
		return jiraProjectResp, nil, saveErr
	}

	project, err := i.JiraProjectRepo.Find(ctx, project.ID)
	if err != nil {
		return jiraProjectResp, nil, err
	}

	return i.buildJiraProjectResponse(project), nil, nil
}
