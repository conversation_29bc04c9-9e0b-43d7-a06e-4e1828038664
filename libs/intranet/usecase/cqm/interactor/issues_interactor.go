// Package interactor provides business logic interactors for the Code Quality Management (CQM) system.
// This file specifically handles SonarQube issue management, Jira integration, and technical debt tracking.
package interactor

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"
	"sync"

	"sa-intranet/core"
	corerepository "sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// Error definitions for SonarQube issue operations
var (
	// ErrSonarIssueAlreadyExists is returned when attempting to create a duplicate SonarQube issue
	ErrSonarIssueAlreadyExists = errors.New("SonarQube issue already exists")
	// ErrJiraProjectNotFound is returned when a corresponding Jira project cannot be found
	ErrJiraProjectNotFound = errors.New("failed to find Jira project for SonarQube project")

	// technicalDebtLabelFormat defines the format for technical debt labels in Jira
	technicalDebtLabelFormat = "technical-debt-%s"
)

// Pagination represents pagination metadata for issue listings.
type Pagination struct {
	// TotalCount is the total number of items across all pages
	TotalCount int `json:"totalCount"`
	// CurrentPage is the current page number (1-based)
	CurrentPage int `json:"currentPage"`
	// PerPage is the number of items per page
	PerPage int `json:"perPage"`
}

type Data struct {
	SonarIssues []SonarIssue `json:"sonarIssues"`
	ProjectID   string       `json:"projectID"`
	Pagination  Pagination   `json:"pagination"`
	JiraProject JiraProject  `json:"jiraProject"`
}

type SonarIssue struct {
	Key          string                  `json:"key"`
	Severity     string                  `json:"severity"`
	Message      string                  `json:"message"`
	Component    string                  `json:"component"`
	Status       string                  `json:"status"`
	CreationDate sonarqube.SonarQubeTime `json:"creationDate"`
	UpdateDate   sonarqube.SonarQubeTime `json:"updateDate"`
	URL          string                  `json:"url"`
	SonarURL     string                  `json:"sonarURL"`
	JiraIssueKey string                  `json:"jiraIssueKey"`
	Effort       string                  `json:"effort"`
	Author       string                  `json:"author"`
	Tags         string                  `json:"tags"`
}

type SonarIssuesInteractor struct {
	sonarqube        *sonarqube.Client
	sonarProjectRepo repository.SonarqubeProjectRepository
	sonarIssueRepo   repository.SonarqubeIssueRepository
	jiraClient       *jira.Client
	cypher           core.Cypher
	sonarConfig      sonarqube.ClientConfig
	jiraConfig       jira.ClientConfig
}

func NewSonarIssuesInteractor(i *do.Injector) (*SonarIssuesInteractor, error) {
	sonarqubeClient, err := do.Invoke[*sonarqube.Client](i)
	if err != nil {
		return nil, err
	}

	sonarProjectRepo, err := do.Invoke[repository.SonarqubeProjectRepository](i)
	if err != nil {
		return nil, err
	}

	sonarIssueRepo, err := do.Invoke[repository.SonarqubeIssueRepository](i)
	if err != nil {
		return nil, err
	}

	jiraClient, err := do.Invoke[*jira.Client](i)
	if err != nil {
		return nil, err
	}

	cypher, err := do.Invoke[core.Cypher](i)
	if err != nil {
		return nil, err
	}

	sonarConfig, err := do.Invoke[sonarqube.ClientConfig](i)
	if err != nil {
		return nil, err
	}

	jiraConfig, err := do.Invoke[jira.ClientConfig](i)
	if err != nil {
		return nil, err
	}

	return &SonarIssuesInteractor{
		sonarqube:        sonarqubeClient,
		sonarProjectRepo: sonarProjectRepo,
		sonarIssueRepo:   sonarIssueRepo,
		jiraClient:       jiraClient,
		cypher:           cypher,
		sonarConfig:      sonarConfig,
		jiraConfig:       jiraConfig,
	}, nil
}

func (i *SonarIssuesInteractor) InitialData(projectID string, page int, pageSize int) (Data, error) {
	ctx := context.Background()

	sonarProject, err := i.getSonarProject(ctx, projectID)
	if err != nil {
		return Data{}, fmt.Errorf("failed to get sonar project %w", err)
	}

	issuesResp, err := i.GetSonarIssues(sonarProject.ProjectKey, page, pageSize)
	if err != nil {
		return Data{}, fmt.Errorf("failed to get sonar issues %w", err)
	}

	issues, err := i.buildSonarIssuesWithJiraMapping(ctx, issuesResp.Issues, sonarProject, pageSize)
	if err != nil {
		return Data{}, fmt.Errorf("failed to build sonar issues %w", err)
	}

	return i.buildDataResponse(issues, projectID, page, pageSize, issuesResp.Total, sonarProject), nil
}

func (i *SonarIssuesInteractor) getSonarProject(ctx context.Context, projectID string) (*model.SonarqubeProject, error) {
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		return nil, err
	}

	sonarProject, err := i.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return nil, err
	}

	return sonarProject, nil
}

func (i *SonarIssuesInteractor) buildSonarIssuesWithJiraMapping(
	ctx context.Context,
	sonarIssues []sonarqube.Issue,
	sonarProject *model.SonarqubeProject,
	pageSize int,
) ([]SonarIssue, error) {
	issuesIDs := make([]string, 0, len(sonarIssues))
	for _, issue := range sonarIssues {
		issuesIDs = append(issuesIDs, issue.Key)
	}

	params := corerepository.PaginationParams[repository.SonarqubeIssueFilter]{
		Filters: repository.SonarqubeIssueFilter{
			IssueIDs: issuesIDs,
		},
		Page:     1,
		PageSize: pageSize,
	}

	issuesDB, err := i.sonarIssueRepo.List(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to list SonarQube DB issues: %w", err)
	}

	return i.mapSonarIssuesToResponse(sonarIssues, issuesDB.Items, sonarProject), nil
}

func (i *SonarIssuesInteractor) mapSonarIssuesToResponse(
	sonarIssues []sonarqube.Issue,
	issuesDB []model.SonarqubeIssue,
	sonarProject *model.SonarqubeProject,
) []SonarIssue {
	issues := make([]SonarIssue, 0, len(sonarIssues))

	for _, issue := range sonarIssues {
		jiraIssueKey := i.findJiraIssueKey(issue.Key, issuesDB)
		url := i.buildJiraURL(jiraIssueKey, sonarProject)
		sonarURL := i.buildSonarURL(issue.Key, sonarProject.ProjectKey)

		issues = append(issues, SonarIssue{
			Key:          issue.Key,
			Severity:     issue.Severity,
			Message:      issue.Message,
			Component:    issue.Component,
			Status:       issue.Status,
			CreationDate: issue.CreationDate,
			UpdateDate:   issue.UpdateDate,
			URL:          url,
			SonarURL:     sonarURL,
			Effort:       issue.Effort,
			JiraIssueKey: jiraIssueKey,
			Author:       issue.Author,
			Tags:         strings.Join(issue.Tags, ", "),
		})
	}

	return issues
}

func (i *SonarIssuesInteractor) findJiraIssueKey(issueKey string, issuesDB []model.SonarqubeIssue) string {
	for _, issueDB := range issuesDB {
		if issueKey == issueDB.IssueID {
			return issueDB.JiraIssueKey
		}
	}

	return ""
}

func (i *SonarIssuesInteractor) buildJiraURL(jiraIssueKey string, sonarProject *model.SonarqubeProject) string {
	if jiraIssueKey == "" {
		return ""
	}

	return fmt.Sprintf("%s/jira/software/c/projects/%s/issues/%s",
		sonarProject.JiraProject.JiraURL,
		sonarProject.JiraProject.ProjectKey,
		jiraIssueKey)
}

func (i *SonarIssuesInteractor) buildSonarURL(issueKey, projectKey string) string {
	return i.sonarConfig.URL + "/project/issues?id=" + projectKey + "&open=" + issueKey
}

func (i *SonarIssuesInteractor) buildDataResponse(
	issues []SonarIssue,
	projectID string,
	page, pageSize, totalCount int,
	sonarProject *model.SonarqubeProject,
) Data {
	pagination := Pagination{
		TotalCount:  totalCount,
		CurrentPage: page,
		PerPage:     pageSize,
	}

	return Data{
		SonarIssues: issues,
		ProjectID:   projectID,
		Pagination:  pagination,
		JiraProject: JiraProject{
			ID:         sonarProject.JiraProject.ID,
			Name:       sonarProject.JiraProject.Name,
			ProjectKey: sonarProject.JiraProject.ProjectKey,
		},
	}
}

func (i *SonarIssuesInteractor) GetSonarIssues(projectKey string, page int, perPage int) (sonarqube.IssuesResponse, error) {
	filter := &sonarqube.IssueFilter{
		Statuses: []string{"OPEN", "CONFIRMED"},
	}
	pagination := &sonarqube.PaginationOptions{
		PageIndex: page,
		PageSize:  perPage,
	}

	issuesResp, err := i.sonarqube.GetProjectIssues(projectKey, filter, pagination)
	if err != nil {
		return sonarqube.IssuesResponse{}, err
	}

	return issuesResp, nil
}

func (i *SonarIssuesInteractor) CreateJiraIssue(projectID string, issueKey string) (SonarIssue, error) {
	ctx := context.Background()
	var result SonarIssue

	// Validate input parameters first (including UUID parsing)
	sonarProject, err := i.prepareSonarProject(ctx, projectID)
	if err != nil {
		return result, err
	}

	// Then validate business rules
	err = i.validateIssueCreation(ctx, issueKey)
	if err != nil {
		return result, err
	}

	creds, err := i.prepareJiraCredentials(sonarProject.JiraProject)
	if err != nil {
		return result, err
	}

	projectResp, issuesResp, err := i.fetchJiraAndSonarData(creds, sonarProject, issueKey)
	if err != nil {
		return result, err
	}

	issueTypeID := i.determineIssueType(projectResp)
	currentIssue, issueLabels := i.processSonarIssue(issuesResp, issueKey)

	jiraIssue, err := i.createJiraTicket(creds, sonarProject.JiraProject, projectResp.ID, issueTypeID, currentIssue, issueLabels)
	if err != nil {
		return result, err
	}

	err = i.saveSonarIssueMapping(ctx, jiraIssue, issueKey, sonarProject.ID)
	if err != nil {
		return result, err
	}

	return i.buildSonarIssueResponse(currentIssue, jiraIssue, sonarProject, issueKey), nil
}

func (i *SonarIssuesInteractor) validateIssueCreation(ctx context.Context, issueKey string) error {
	iss, err := i.sonarIssueRepo.FindByIssueID(ctx, issueKey)
	if err == nil {
		return fmt.Errorf("%w (%s)", ErrSonarIssueAlreadyExists, iss.JiraIssueKey)
	}

	return nil
}

func (i *SonarIssuesInteractor) prepareSonarProject(ctx context.Context, projectID string) (*model.SonarqubeProject, error) {
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse project ID: %w", err)
	}

	sonarProject, err := i.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to find SonarQube project: %w", err)
	}

	if sonarProject.JiraProject == nil {
		return nil, ErrJiraProjectNotFound
	}

	return sonarProject, nil
}

func (i *SonarIssuesInteractor) prepareJiraCredentials(jiraProject *model.JiraProject) (jira.Credentials, error) {
	token, err := i.cypher.Decrypt(jiraProject.Token)
	if err != nil {
		return jira.Credentials{}, fmt.Errorf("failed to decrypt token: %w", err)
	}

	return jira.Credentials{
		Username: jiraProject.Username,
		Token:    token,
	}, nil
}

func (i *SonarIssuesInteractor) fetchJiraAndSonarData(
	creds jira.Credentials,
	sonarProject *model.SonarqubeProject,
	issueKey string,
) (jira.Project, sonarqube.IssuesResponse, error) {
	var (
		issuesResp     sonarqube.IssuesResponse
		issuesRespErr  error
		projectResp    jira.Project
		projectRespErr error
		wg             sync.WaitGroup
	)

	wg.Add(2)

	go func() {
		defer wg.Done()

		projectResp, projectRespErr = i.jiraClient.GetProject(creds, sonarProject.JiraProject.JiraURL, sonarProject.JiraProject.ProjectKey)
	}()

	go func() {
		defer wg.Done()

		issuesKeys := []string{issueKey}
		filter := &sonarqube.IssueFilter{
			Statuses: []string{},
			Issues:   issuesKeys,
		}
		pagination := &sonarqube.PaginationOptions{
			PageIndex: 1,
			PageSize:  len(issuesKeys),
		}
		issuesResp, issuesRespErr = i.sonarqube.GetProjectIssues(sonarProject.ProjectKey, filter, pagination)
	}()

	wg.Wait()

	if projectRespErr != nil {
		return projectResp, issuesResp, fmt.Errorf("failed to get Jira project: %w", projectRespErr)
	}

	if issuesRespErr != nil {
		return projectResp, issuesResp, fmt.Errorf("failed to get SonarQube issues: %w", issuesRespErr)
	}

	return projectResp, issuesResp, nil
}

func (i *SonarIssuesInteractor) determineIssueType(projectResp jira.Project) string {
	issueTypeID := ""
	if len(projectResp.IssueTypes) > 0 {
		issueTypeID = projectResp.IssueTypes[0].ID
	}

	for _, issueType := range projectResp.IssueTypes {
		if issueType.Name == "Technical Debt" {
			issueTypeID = issueType.ID
			break
		}
	}

	return issueTypeID
}

func (i *SonarIssuesInteractor) processSonarIssue(
	issuesResp sonarqube.IssuesResponse,
	issueKey string,
) (sonarqube.Issue, []string) {
	var currentIssue sonarqube.Issue
	issueLabels := []string{"sonarqube", "technical-debt"}

	for _, issue := range issuesResp.Issues {
		if issue.Key == issueKey {
			currentIssue = issue
			issueLabels = append(issueLabels, fmt.Sprintf(technicalDebtLabelFormat, strings.ToLower(issue.Severity)))

			for _, tag := range issue.Tags {
				issueLabels = append(issueLabels, fmt.Sprintf(technicalDebtLabelFormat, tag))
			}

			issueTypeLabel := fmt.Sprintf(technicalDebtLabelFormat, strings.ToLower(issue.Type))
			if !slices.Contains(issueLabels, issueTypeLabel) {
				issueLabels = append(issueLabels, issueTypeLabel)
			}

			break
		}
	}

	return currentIssue, issueLabels
}

func (i *SonarIssuesInteractor) createJiraTicket(
	creds jira.Credentials,
	jiraProject *model.JiraProject,
	projectID,
	issueTypeID string,
	currentIssue sonarqube.Issue,
	issueLabels []string,
) (jira.Issue, error) {
	issueDetails := fmt.Sprintf(
		"Issue Key: %s\nSeverity: %s\nEffort: %s\nStatus: %s\nLink: %s",
		currentIssue.Key,
		currentIssue.Severity,
		currentIssue.Effort,
		currentIssue.Status,
		i.sonarConfig.URL+"/project/issues?id="+jiraProject.ProjectKey+"&open="+currentIssue.Key,
	)
	issueMessage := currentIssue.Message
	issueDetailsTrucated := strings.ReplaceAll(strings.ReplaceAll(issueMessage, "\r", ""), "\n", "")
	truncateLimit := 80

	if len(issueDetailsTrucated) > truncateLimit {
		issueDetailsTrucated = issueDetailsTrucated[:truncateLimit] + "..."
	}

	issueMessage = fmt.Sprintf("SonarQube Issue: %s", issueDetailsTrucated)

	summary := fmt.Sprintf("[Tech Debt] - %s", issueMessage)
	description := &jira.Description{
		Content: []jira.ContentContainer{
			{
				Type: "paragraph",
				Content: []jira.Content{
					{
						Type: "text",
						Text: issueDetails,
					},
				},
			},
		},
		Type:    "doc",
		Version: 1,
	}

	createIssueResp, err := i.jiraClient.CreateIssue(creds, jiraProject.JiraURL, projectID, issueTypeID, summary, description, issueLabels)
	if err != nil {
		return jira.Issue{}, err
	}

	return jira.Issue{
		ID:  createIssueResp.ID,
		Key: createIssueResp.Key,
	}, nil
}

func (i *SonarIssuesInteractor) saveSonarIssueMapping(ctx context.Context, jiraIssue jira.Issue, issueKey string, sonarProjectID uuid.UUID) error {
	sonarIssueReq := &model.SonarqubeIssue{}

	sonarIssueReq.JiraIssueID = jiraIssue.ID
	sonarIssueReq.JiraIssueKey = jiraIssue.Key
	sonarIssueReq.IssueID = issueKey
	sonarIssueReq.SonarqubeProjectID = sonarProjectID

	err := i.sonarIssueRepo.Save(ctx, sonarIssueReq)
	if err != nil {
		return err
	}

	return nil
}

func (i *SonarIssuesInteractor) buildSonarIssueResponse(
	currentIssue sonarqube.Issue,
	jiraIssue jira.Issue,
	sonarProject *model.SonarqubeProject,
	issueKey string,
) SonarIssue {
	url := fmt.Sprintf("%s/jira/software/c/projects/%s/issues/%s", sonarProject.JiraProject.JiraURL, sonarProject.JiraProject.ProjectKey, jiraIssue.Key)

	return SonarIssue{
		Key:          issueKey,
		Severity:     currentIssue.Severity,
		Message:      currentIssue.Message,
		Component:    currentIssue.Component,
		Status:       currentIssue.Status,
		CreationDate: currentIssue.CreationDate,
		UpdateDate:   currentIssue.UpdateDate,
		URL:          url,
		JiraIssueKey: jiraIssue.Key,
	}
}
