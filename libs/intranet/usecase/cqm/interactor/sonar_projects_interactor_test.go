package interactor_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	validationFailedMsg  = "validation errors occurred"
	jiraProjectExistsMsg = "validation errors occurred"
	testProjectKey       = "TEST_PROJECT"
	testProjectName      = "Test Project"
	testBranch           = "main"
	expectedErrorMsg     = "Expected error but got none"
	unexpectedErrorMsg   = "Unexpected error: %v"
	testJiraURL          = "https://test.atlassian.net"
	testEmail            = "<EMAIL>"
	encryptedToken       = "encrypted_token"
	contentTypeHeader    = "Content-Type"
	applicationJSON      = "application/json"
)

func setupSonarQubeMockServerForProjects() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		case r.URL.Path == "/api/components/search":
			// Handle project search requests - this is what GetProject() calls
			projectKey := r.URL.Query().Get("q")
			qualifiers := r.URL.Query().Get("qualifiers")

			// Validate that this is a project search request
			if qualifiers != "TRK" {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			if projectKey == "" {
				projectKey = testProjectKey
			}

			w.Header().Set(contentTypeHeader, applicationJSON)
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"components": [
					{
						"key": "` + projectKey + `",
						"name": "` + testProjectName + `"
					}
				]
			}`))
		case r.URL.Path == "/api/projects/search":
			// Handle project search requests for other endpoints
			projectKey := r.URL.Query().Get("projects")
			if projectKey == "" {
				projectKey = testProjectKey
			}
			w.Header().Set(contentTypeHeader, applicationJSON)
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"components": [
					{
						"key": "` + projectKey + `",
						"name": "` + testProjectName + `"
					}
				]
			}`))
		case r.URL.Path == "/api/components/show":
			// Handle individual project lookup
			projectKey := r.URL.Query().Get("component")
			if projectKey == "" {
				projectKey = testProjectKey
			}
			w.Header().Set(contentTypeHeader, applicationJSON)
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
				"component": {
					"key": "` + projectKey + `",
					"name": "` + testProjectName + `"
				}
			}`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestSonarProjectsInteractorInitialDataComprehensive(t *testing.T) {
	tests := []struct {
		name     string
		filter   string
		page     int
		pageSize int
		wantErr  bool
		setup    func(t *testing.T)
		verify   func(t *testing.T, got interactor.SonarProjectsData)
	}{
		{
			name:     "success case - list all projects",
			filter:   "",
			page:     1,
			pageSize: 10,
			wantErr:  false,
			setup: func(t *testing.T) {
				// Create test data
				companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](injector)
				jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](injector)
				sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](injector)

				// Create company client
				companyClient := &model.CompanyClient{
					Name: "Test Company for Sonar",
				}
				savedCompany, err := companyClientRepo.Save(context.Background(), companyClient)
				require.NoError(t, err)

				// Create Jira project
				jiraProject := &model.JiraProject{
					Name:            "Test Jira Project for Sonar",
					ProjectKey:      "TJPS",
					CompanyClientID: savedCompany.ID,
					JiraURL:         "https://test.atlassian.net",
					Username:        "<EMAIL>",
					Token:           "encrypted_token",
					Active:          true,
				}
				savedJira, err := jiraProjectRepo.Save(context.Background(), jiraProject)
				require.NoError(t, err)

				// Create Sonar project
				sonarProject := &model.SonarqubeProject{
					ProjectKey:    "test_sonar_project",
					ProjectName:   "Test Sonar Project",
					JiraProjectID: savedJira.ID,
					Branch:        testBranch,
					Active:        true,
				}
				_, err = sonarProjectRepo.Save(context.Background(), sonarProject, true)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, got interactor.SonarProjectsData) {
				assert.GreaterOrEqual(t, len(got.SonarProjects), 1, "Should have at least 1 project")
				assert.Equal(t, 1, got.Pagination.CurrentPage, "Current page should be 1")
				assert.Equal(t, 10, got.Pagination.PerPage, "Per page should be 10")
				assert.Greater(t, got.Pagination.TotalCount, 0, "Total count should be greater than 0")
			},
		},
		{
			name:     "success case - filter by name",
			filter:   "Test Sonar",
			page:     1,
			pageSize: 5,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.SonarProjectsData) {
				assert.Equal(t, 1, got.Pagination.CurrentPage, "Current page should be 1")
				assert.Equal(t, 5, got.Pagination.PerPage, "Per page should be 5")
			},
		},
		{
			name:     "success case - large page size",
			filter:   "",
			page:     1,
			pageSize: 100,
			wantErr:  false,
			verify: func(t *testing.T, got interactor.SonarProjectsData) {
				assert.Equal(t, 1, got.Pagination.CurrentPage, "Current page should be 1")
				assert.Equal(t, 100, got.Pagination.PerPage, "Per page should be 100")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t)
			}

			interactor := do.MustInvoke[*interactor.SonarProjectsInteractor](injector)
			got, err := interactor.InitialData(tt.filter, tt.page, tt.pageSize)

			if tt.wantErr {
				require.Error(t, err, expectedErrorMsg)
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarProjectsInteractorCreateSonarProject(t *testing.T) {
	tests := []struct {
		name            string
		input           interactor.SonarProjectValidator
		wantErr         bool
		errContains     string
		setup           func(t *testing.T) uuid.UUID
		mockSonarServer func() *httptest.Server
		verify          func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator)
	}{
		{
			name: "error case - empty project key",
			input: interactor.SonarProjectValidator{
				ProjectKey: "",
				Branch:     testBranch,
				Active:     &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: validationFailedMsg,
			setup: func(t *testing.T) uuid.UUID {
				return uuid.New() // Return any UUID for JiraProjectID
			},
		},
		{
			name: "error case - empty branch",
			input: interactor.SonarProjectValidator{
				ProjectKey: testProjectKey,
				Branch:     "",
				Active:     &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: validationFailedMsg,
			setup: func(t *testing.T) uuid.UUID {
				return uuid.New()
			},
		},
		{
			name: "error case - nil active",
			input: interactor.SonarProjectValidator{
				ProjectKey: testProjectKey,
				Branch:     testBranch,
				Active:     nil,
			},
			wantErr:     true,
			errContains: validationFailedMsg,
			setup: func(t *testing.T) uuid.UUID {
				return uuid.New()
			},
		},
		{
			name: "error case - invalid jira project ID",
			input: interactor.SonarProjectValidator{
				ProjectKey:    testProjectKey,
				JiraProjectID: uuid.New(), // Non-existent UUID
				Branch:        testBranch,
				Active:        &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: jiraProjectExistsMsg,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data and get Jira project ID
			var jiraProjectID uuid.UUID
			if tt.setup != nil {
				jiraProjectID = tt.setup(t)
				tt.input.JiraProjectID = jiraProjectID
			}

			interactor := do.MustInvoke[*interactor.SonarProjectsInteractor](injector)
			got, validationErrors, err := interactor.CreateSonarProject(tt.input)

			if tt.wantErr {
				if err != nil {
					if tt.errContains != "" {
						assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
					}
				} else if validationErrors != nil {
					// Check validation errors
					assert.NotEmpty(t, validationErrors, "Should have validation errors")
				} else {
					t.Error("Expected error or validation errors but got none")
				}
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)
			require.Nil(t, validationErrors, "Should not have validation errors")

			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}

func TestSonarProjectsInteractorUpdateSonarProject(t *testing.T) {
	tests := []struct {
		name        string
		projectID   string
		input       interactor.SonarProjectValidator
		wantErr     bool
		errContains string
		setup       func(t *testing.T) (string, uuid.UUID)
		verify      func(t *testing.T, got interactor.SonarProject, input interactor.SonarProjectValidator)
	}{
		{
			name:      "error case - invalid project ID",
			projectID: "invalid-uuid",
			input: interactor.SonarProjectValidator{
				ProjectKey: testProjectKey,
				Branch:     testBranch,
				Active:     &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: "failed to parse project ID",
		},
		{
			name:      "error case - non-existent project",
			projectID: uuid.New().String(),
			input: interactor.SonarProjectValidator{
				ProjectKey: testProjectKey,
				Branch:     testBranch,
				Active:     &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: "sql: no rows in result set",
		},
		{
			name: "error case - validation failure",
			input: interactor.SonarProjectValidator{
				ProjectKey: "", // Empty project key
				Branch:     testBranch,
				Active:     &[]bool{true}[0],
			},
			wantErr:     true,
			errContains: validationFailedMsg,
			setup: func(t *testing.T) (string, uuid.UUID) {
				// Create minimal test data
				companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](injector)
				jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](injector)
				sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](injector)

				companyClient := &model.CompanyClient{Name: "Test Company"}
				savedCompany, err := companyClientRepo.Save(context.Background(), companyClient)
				require.NoError(t, err)

				jiraProject := &model.JiraProject{
					Name:            "Test Jira",
					ProjectKey:      "TJ",
					CompanyClientID: savedCompany.ID,
					JiraURL:         "https://test.atlassian.net",
					Username:        "<EMAIL>",
					Token:           "token",
					Active:          true,
				}
				savedJira, err := jiraProjectRepo.Save(context.Background(), jiraProject)
				require.NoError(t, err)

				sonarProject := &model.SonarqubeProject{
					ProjectKey:    "TEST_PROJECT",
					ProjectName:   "Test Project",
					JiraProjectID: savedJira.ID,
					Branch:        testBranch,
					Active:        true,
				}
				savedSonar, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
				require.NoError(t, err)

				return savedSonar.ID.String(), savedJira.ID
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data
			var projectID string
			var jiraProjectID uuid.UUID
			if tt.setup != nil {
				projectID, jiraProjectID = tt.setup(t)
				tt.input.JiraProjectID = jiraProjectID
			} else {
				projectID = tt.projectID
			}

			interactor := do.MustInvoke[*interactor.SonarProjectsInteractor](injector)
			got, validationErrors, err := interactor.UpdateSonarProject(projectID, tt.input)

			if tt.wantErr {
				if err != nil {
					if tt.errContains != "" {
						assert.Contains(t, err.Error(), tt.errContains, "Error should contain expected text")
					}
				} else if validationErrors != nil {
					assert.NotEmpty(t, validationErrors, "Should have validation errors")
				} else {
					t.Error("Expected error or validation errors but got none")
				}
				return
			}

			require.NoError(t, err, unexpectedErrorMsg, err)
			require.Nil(t, validationErrors, "Should not have validation errors")

			if tt.verify != nil {
				tt.verify(t, got, tt.input)
			}
		})
	}
}
