package interactor

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	corerepository "sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupMockSonarQubeServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		case r.URL.Path == "/api/components/search":
			projectKey := r.URL.Query().Get("q")
			if projectKey == "NONEXISTENT_PROJECT" {
				w.WriteHeader(http.StatusNotFound)
				w.Write([]byte(`{"error": "Project not found"}`))
				return
			}

			w.Header().Set("Content-Type", "application/json")
			w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
			w.Write([]byte(`{
				"components": [
					{
						"key": "` + projectKey + `",
						"name": "Test Project",
						"qualifier": "TRK"
					}
				]
			}`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func setupTestSonarProjectsInteractor(t *testing.T, serverURL string) *SonarProjectsInteractor {
	injector := do.New()

	// Mock SonarQube client config
	do.Provide(injector, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return sonarqube.ClientConfig{
			URL:   serverURL,
			Token: "test-token",
		}, nil
	})

	// Mock repository
	do.Provide(injector, func(i *do.Injector) (repository.SonarqubeProjectRepository, error) {
		return &mockSonarProjectRepo{}, nil
	})

	// Provide SonarQube client
	do.Provide(injector, func(i *do.Injector) (*sonarqube.Client, error) {
		return sonarqube.New(i)
	})

	// Create SonarQube client
	sonarClient := do.MustInvoke[*sonarqube.Client](injector)

	return &SonarProjectsInteractor{
		sonarProjectRepo: &mockSonarProjectRepo{},
		sonarqube:        sonarClient,
	}
}

type mockSonarProjectRepo struct {
	savedProjects map[uuid.UUID]*model.SonarqubeProject
}

func (m *mockSonarProjectRepo) Find(ctx context.Context, id uuid.UUID) (*model.SonarqubeProject, error) {
	// Initialize map if nil
	if m.savedProjects == nil {
		m.savedProjects = make(map[uuid.UUID]*model.SonarqubeProject)
	}

	// Return a project that matches what was saved
	if project, exists := m.savedProjects[id]; exists {
		return project, nil
	}

	// Return a default project for testing
	return &model.SonarqubeProject{
		ID:          id,
		ProjectKey:  "TEST_PROJECT",
		ProjectName: "Test Project",
		Active:      true,
		Branch:      "main",
		JiraProject: &model.JiraProject{
			ID:     uuid.New(),
			Name:   "Test Jira Project",
			Active: true,
			CompanyClient: &model.CompanyClient{
				ID:   uuid.New(),
				Name: "Test Company",
			},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

func (m *mockSonarProjectRepo) List(ctx context.Context, params corerepository.PaginationParams[repository.SonarqubeProjectFilter]) (*corerepository.PaginatedResult[model.SonarqubeProject], error) {
	return &corerepository.PaginatedResult[model.SonarqubeProject]{
		Items:      []model.SonarqubeProject{},
		TotalItems: 0,
		TotalPages: 0,
	}, nil
}

func (m *mockSonarProjectRepo) Save(ctx context.Context, project *model.SonarqubeProject, forceID bool) (*model.SonarqubeProject, error) {
	// Initialize map if nil
	if m.savedProjects == nil {
		m.savedProjects = make(map[uuid.UUID]*model.SonarqubeProject)
	}

	if project.ID == uuid.Nil {
		project.ID = uuid.New()
	}
	// Store the saved project for later retrieval by ID
	m.savedProjects[project.ID] = project
	return project, nil
}

func TestSonarProjectsInteractor_buildUpdatedProject(t *testing.T) {
	server := setupMockSonarQubeServer()
	defer server.Close()

	interactor := setupTestSonarProjectsInteractor(t, server.URL)

	projectUUID := uuid.New()
	jiraProjectID := uuid.New()
	active := true

	tests := []struct {
		name            string
		projectUUID     uuid.UUID
		sonarProject    SonarProjectValidator
		existingProject *model.SonarqubeProject
		wantErr         bool
		verify          func(t *testing.T, result *model.SonarqubeProject, err error)
	}{
		{
			name:        "successful_build_with_same_project_key",
			projectUUID: projectUUID,
			sonarProject: SonarProjectValidator{
				ProjectKey:    "TEST_PROJECT",
				JiraProjectID: jiraProjectID,
				Branch:        "main",
				Active:        &active,
			},
			existingProject: &model.SonarqubeProject{
				ID:            projectUUID,
				ProjectKey:    "TEST_PROJECT",
				ProjectName:   "Existing Project Name",
				JiraProjectID: jiraProjectID,
				Branch:        "old-branch",
				Active:        false,
				CreatedAt:     time.Now().Add(-time.Hour),
				UpdatedAt:     time.Now().Add(-time.Hour),
			},
			wantErr: false,
			verify: func(t *testing.T, result *model.SonarqubeProject, err error) {
				require.NoError(t, err)
				assert.Equal(t, projectUUID, result.ID)
				assert.Equal(t, "TEST_PROJECT", result.ProjectKey)
				assert.Equal(t, "Existing Project Name", result.ProjectName) // Should keep existing name
				assert.Equal(t, jiraProjectID, result.JiraProjectID)
				assert.Equal(t, "main", result.Branch)
				assert.True(t, result.Active)
			},
		},
		{
			name:        "successful_build_with_different_project_key",
			projectUUID: projectUUID,
			sonarProject: SonarProjectValidator{
				ProjectKey:    "NEW_PROJECT_KEY",
				JiraProjectID: jiraProjectID,
				Branch:        "main",
				Active:        &active,
			},
			existingProject: &model.SonarqubeProject{
				ID:            projectUUID,
				ProjectKey:    "OLD_PROJECT_KEY",
				ProjectName:   "Old Project Name",
				JiraProjectID: jiraProjectID,
				Branch:        "old-branch",
				Active:        false,
				CreatedAt:     time.Now().Add(-time.Hour),
				UpdatedAt:     time.Now().Add(-time.Hour),
			},
			wantErr: false,
			verify: func(t *testing.T, result *model.SonarqubeProject, err error) {
				require.NoError(t, err)
				assert.Equal(t, projectUUID, result.ID)
				assert.Equal(t, "NEW_PROJECT_KEY", result.ProjectKey)
				assert.Equal(t, "Test Project", result.ProjectName) // Should get new name from SonarQube
				assert.Equal(t, jiraProjectID, result.JiraProjectID)
				assert.Equal(t, "main", result.Branch)
				assert.True(t, result.Active)
			},
		},
		{
			name:        "error_when_sonarqube_project_not_found",
			projectUUID: projectUUID,
			sonarProject: SonarProjectValidator{
				ProjectKey:    "NONEXISTENT_PROJECT",
				JiraProjectID: jiraProjectID,
				Branch:        "main",
				Active:        &active,
			},
			existingProject: &model.SonarqubeProject{
				ID:            projectUUID,
				ProjectKey:    "OLD_PROJECT_KEY",
				ProjectName:   "Old Project Name",
				JiraProjectID: jiraProjectID,
				Branch:        "old-branch",
				Active:        false,
				CreatedAt:     time.Now().Add(-time.Hour),
				UpdatedAt:     time.Now().Add(-time.Hour),
			},
			wantErr: true,
			verify: func(t *testing.T, result *model.SonarqubeProject, err error) {
				assert.Error(t, err)
				assert.Nil(t, result)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := interactor.buildUpdatedProject(tt.projectUUID, tt.sonarProject, tt.existingProject)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.verify != nil {
				tt.verify(t, result, err)
			}
		})
	}
}

func TestSonarProjectsInteractor_saveAndReturnUpdatedProject(t *testing.T) {
	server := setupMockSonarQubeServer()
	defer server.Close()

	interactor := setupTestSonarProjectsInteractor(t, server.URL)

	tests := []struct {
		name        string
		project     *model.SonarqubeProject
		projectUUID uuid.UUID
		wantErr     bool
		verify      func(t *testing.T, result SonarProject, validationErrors map[string]any, err error)
	}{
		{
			name:        "successful_save_and_return",
			projectUUID: uuid.New(),
			project: &model.SonarqubeProject{
				ID:            uuid.New(),
				ProjectKey:    "TEST_PROJECT",
				ProjectName:   "Test Project",
				JiraProjectID: uuid.New(),
				Branch:        "main",
				Active:        true,
				JiraProject: &model.JiraProject{
					ID:     uuid.New(),
					Name:   "Test Jira Project",
					Active: true,
					CompanyClient: &model.CompanyClient{
						ID:   uuid.New(),
						Name: "Test Company",
					},
				},
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			wantErr: false,
			verify: func(t *testing.T, result SonarProject, validationErrors map[string]any, err error) {
				require.NoError(t, err)
				assert.Nil(t, validationErrors)
				assert.Equal(t, "TEST_PROJECT", result.ProjectKey)
				assert.Equal(t, "Test Project", result.ProjectName)
				assert.True(t, result.Active)
				assert.NotNil(t, result.JiraProject)
				assert.Equal(t, "Test Jira Project", result.JiraProject.Name)
			},
		},
		{
			name:        "successful_save_with_nil_id_generates_new_id",
			projectUUID: uuid.New(),
			project: &model.SonarqubeProject{
				ID:            uuid.Nil,
				ProjectKey:    "NEW_PROJECT",
				ProjectName:   "New Project",
				JiraProjectID: uuid.New(),
				Branch:        "develop",
				Active:        true,
				JiraProject: &model.JiraProject{
					ID:     uuid.New(),
					Name:   "New Jira Project",
					Active: true,
					CompanyClient: &model.CompanyClient{
						ID:   uuid.New(),
						Name: "New Company",
					},
				},
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			wantErr: false,
			verify: func(t *testing.T, result SonarProject, validationErrors map[string]any, err error) {
				require.NoError(t, err)
				assert.Nil(t, validationErrors)
				assert.NotEqual(t, uuid.Nil, result.ID) // Should have generated a new ID
				assert.Equal(t, "NEW_PROJECT", result.ProjectKey)
				assert.Equal(t, "New Project", result.ProjectName)
				assert.True(t, result.Active)
				assert.NotNil(t, result.JiraProject)
				assert.Equal(t, "New Jira Project", result.JiraProject.Name)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, validationErrors, err := interactor.saveAndReturnUpdatedProject(ctx, tt.project)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.verify != nil {
				tt.verify(t, result, validationErrors, err)
			}
		})
	}
}

func TestSonarProjectsInteractor_buildSonarProjectResponse(t *testing.T) {
	tests := []struct {
		name    string
		project *model.SonarqubeProject
		verify  func(t *testing.T, result SonarProject)
	}{
		{
			name: "successful_build_response_with_complete_project",
			project: &model.SonarqubeProject{
				ID:            uuid.New(),
				ProjectKey:    "TEST_PROJECT",
				ProjectName:   "Test Project",
				JiraProjectID: uuid.New(),
				Branch:        "main",
				Active:        true,
				JiraProject: &model.JiraProject{
					ID:     uuid.New(),
					Name:   "Test Jira Project",
					Active: true,
					CompanyClient: &model.CompanyClient{
						ID:   uuid.New(),
						Name: "Test Company",
					},
				},
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			verify: func(t *testing.T, result SonarProject) {
				assert.Equal(t, "TEST_PROJECT", result.ProjectKey)
				assert.Equal(t, "Test Project", result.ProjectName)
				assert.Equal(t, "main", result.Branch)
				assert.True(t, result.Active)
				assert.NotNil(t, result.JiraProject)
				assert.Equal(t, "Test Jira Project", result.JiraProject.Name)
				assert.True(t, result.JiraProject.Active)
				assert.NotNil(t, result.JiraProject.CompanyClient)
				assert.Equal(t, "Test Company", result.JiraProject.CompanyClient.Name)
			},
		},
		{
			name: "successful_build_response_with_nil_jira_project",
			project: &model.SonarqubeProject{
				ID:            uuid.New(),
				ProjectKey:    "ORPHAN_PROJECT",
				ProjectName:   "Orphan Project",
				JiraProjectID: uuid.New(),
				Branch:        "develop",
				Active:        false,
				JiraProject:   nil, // No associated Jira project - should handle gracefully
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
			verify: func(t *testing.T, result SonarProject) {
				assert.Equal(t, "ORPHAN_PROJECT", result.ProjectKey)
				assert.Equal(t, "Orphan Project", result.ProjectName)
				assert.Equal(t, "develop", result.Branch)
				assert.False(t, result.Active)
				assert.Nil(t, result.JiraProject) // Should handle nil gracefully
			},
		},
		{
			name: "successful_build_response_with_jira_project_but_nil_company",
			project: &model.SonarqubeProject{
				ID:            uuid.New(),
				ProjectKey:    "PARTIAL_PROJECT",
				ProjectName:   "Partial Project",
				JiraProjectID: uuid.New(),
				Branch:        "feature/test",
				Active:        true,
				JiraProject: &model.JiraProject{
					ID:            uuid.New(),
					Name:          "Partial Jira Project",
					Active:        true,
					CompanyClient: nil, // No associated company client
				},
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			verify: func(t *testing.T, result SonarProject) {
				assert.Equal(t, "PARTIAL_PROJECT", result.ProjectKey)
				assert.Equal(t, "Partial Project", result.ProjectName)
				assert.Equal(t, "feature/test", result.Branch)
				assert.True(t, result.Active)
				assert.NotNil(t, result.JiraProject)
				assert.Equal(t, "Partial Jira Project", result.JiraProject.Name)
				assert.True(t, result.JiraProject.Active)
				assert.Nil(t, result.JiraProject.CompanyClient) // Should handle nil gracefully
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal interactor instance for testing
			interactor := &SonarProjectsInteractor{}

			result := interactor.buildSonarProjectResponse(tt.project)

			if tt.verify != nil {
				tt.verify(t, result)
			}
		})
	}
}
