// Package interactor provides business logic interactors for the Code Quality Management (CQM) system.
// It implements Clean Architecture principles by containing use case specific business logic
// for SonarQube analysis, issue management, and project integration workflows.
package interactor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"time"

	"sa-intranet/core"
	corerepository "sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/samber/do"
)

// Error definitions for SonarQube analysis operations
var (
	// ErrProjectNotFound is returned when a SonarQube project cannot be located
	ErrProjectNotFound = errors.New("sonarqube project not found")
	// ErrInvalidBranchType is returned when the branch type is not supported for analysis
	ErrInvalidBranchType = errors.New("branch type must be 'BRANCH' in order to be processed")
	// ErrInvalidBranchName is returned when the branch name doesn't match expected patterns
	ErrInvalidBranchName = errors.New("branch name does not match expected value")
	// ErrCreateSonarAnalysis is returned when SonarQube analysis creation fails
	ErrCreateSonarAnalysis = errors.New("failed to create SonarQube analysis")
	// ErrFetchSonarMeasures is returned when retrieving SonarQube measures fails
	ErrFetchSonarMeasures = errors.New("failed to fetch SonarQube measures")
	// ErrFetchSonarIssues is returned when retrieving SonarQube issues fails
	ErrFetchSonarIssues = errors.New("failed to fetch SonarQube issues")
	// ErrAnalysisAlreadyProcessed is returned when attempting to process an already completed analysis
	ErrAnalysisAlreadyProcessed = errors.New("analysis has already been processed")
)

// SonarQubeWebhook represents the complete webhook payload sent by SonarQube
// after analysis completion. It contains all necessary information for processing
// code quality metrics and quality gate results.
type SonarQubeWebhook struct {
	// ServerURL is the SonarQube server URL that sent the webhook
	ServerURL string `json:"serverUrl"`
	// TaskID is the unique identifier for the analysis task
	TaskID string `json:"taskId"`
	// Status indicates the analysis completion status (SUCCESS, FAILED, etc.)
	Status string `json:"status"`
	// AnalysedAt is the timestamp when the analysis was completed
	AnalysedAt string `json:"analysedAt"`
	// Revision is the SCM revision that was analyzed
	Revision string `json:"revision"`
	// ChangedAt is the timestamp when the analysis status changed
	ChangedAt string `json:"changedAt"`
	// Project contains information about the analyzed project
	Project SonarQubeWebhookProject `json:"project"`
	// Branch contains information about the analyzed branch
	Branch SonarQubeWebhookBranch `json:"branch"`
	// QualityGate contains the quality gate evaluation results
	QualityGate SonarQubeWebhookQualityGate `json:"qualityGate"`
	// Properties contains additional analysis properties
	Properties SonarQubeWebhookProperties `json:"properties"`
}

// SonarQubeWebhookProject represents project information in the webhook payload.
type SonarQubeWebhookProject struct {
	// Key is the unique project identifier in SonarQube
	Key string `json:"key"`
	// Name is the human-readable project name
	Name string `json:"name"`
	// URL is the direct link to the project in SonarQube
	URL string `json:"url"`
}

// SonarQubeWebhookBranch represents branch information in the webhook payload.
type SonarQubeWebhookBranch struct {
	// Name is the branch name that was analyzed
	Name string `json:"name"`
	// Type indicates the branch type (BRANCH, PULL_REQUEST, etc.)
	Type string `json:"type"`
	// IsMain indicates if this is the main/master branch
	IsMain bool `json:"isMain"`
	// URL is the direct link to the branch analysis in SonarQube
	URL string `json:"url"`
}

// SonarQubeWebhookQualityGate represents quality gate evaluation results.
type SonarQubeWebhookQualityGate struct {
	// Name is the quality gate name that was applied
	Name string `json:"name"`
	// Status indicates if the quality gate passed (OK, ERROR, etc.)
	Status string `json:"status"`
	// Conditions contains individual metric evaluations
	Conditions []SonarQubeWebhookCondition `json:"conditions"`
}

// SonarQubeWebhookCondition represents a single quality gate condition evaluation.
type SonarQubeWebhookCondition struct {
	// Metric is the name of the evaluated metric
	Metric string `json:"metric"`
	// Operator is the comparison operator used (GT, LT, etc.)
	Operator string `json:"operator"`
	// Value is the actual measured value
	Value string `json:"value"`
	// Status indicates if the condition passed (OK, ERROR, etc.)
	Status string `json:"status"`
	// ErrorThreshold is the threshold value that triggers an error
	ErrorThreshold string `json:"errorThreshold"`
}

// SonarQubeWebhookProperties contains additional analysis metadata.
type SonarQubeWebhookProperties struct {
	// DetectedSCM is the detected source control management system
	DetectedSCM string `json:"sonar.analysis.detectedscm"`
	// DetectedCI is the detected continuous integration system
	DetectedCI string `json:"sonar.analysis.detectedci"`
}

// SonarAnalizerInteractor handles SonarQube webhook processing and analysis creation.
// It orchestrates the complete workflow from webhook reception to analysis persistence,
// including quality scoring and metrics collection.
type SonarAnalizerInteractor struct {
	// sonarqube provides access to SonarQube API operations
	sonarqube *sonarqube.Client
	// sonarProjectRepo handles SonarQube project data persistence
	sonarProjectRepo repository.SonarqubeProjectRepository
	// sonarqubeAnalysisRepo handles analysis data persistence
	sonarqubeAnalysisRepo repository.SonarqubeAnalysisRepository
}

// NewSonarAnalizerInteractor creates a new SonarQube analyzer interactor with dependency injection.
// It initializes all required dependencies for processing SonarQube webhooks and managing analyses.
//
// Parameters:
//   - i: Dependency injection container containing required services
//
// Returns a configured SonarAnalizerInteractor instance or an error if injection fails.
func NewSonarAnalizerInteractor(i *do.Injector) (*SonarAnalizerInteractor, error) {
	sonarqubeClient, err := do.Invoke[*sonarqube.Client](i)
	if err != nil {
		return nil, err
	}

	sonarProjectRepo, err := do.Invoke[repository.SonarqubeProjectRepository](i)
	if err != nil {
		return nil, err
	}

	interactor := &SonarAnalizerInteractor{
		sonarqube:        sonarqubeClient,
		sonarProjectRepo: sonarProjectRepo,
	}

	sonarqubeAnalysisRepo, err := do.Invoke[repository.SonarqubeAnalysisRepository](i)
	if err != nil {
		return nil, err
	}

	interactor.sonarqubeAnalysisRepo = sonarqubeAnalysisRepo

	return interactor, nil
}

// AnalizeOptions provides configuration options for analysis processing.
// Currently empty but allows for future extensibility.
type AnalizeOptions struct{}

// AnalizeResponse represents the response from SonarQube analysis processing.
// It contains the calculated quality score and analysis status information.
type AnalizeResponse struct {
	Data struct {
		// Score is the calculated quality score based on SonarQube metrics
		Score float64 `json:"score"`
		// QualityGateStatus indicates if the quality gate passed or failed
		QualityGateStatus string `json:"quality_gate_status"`
		// AnalysisStatus indicates the current processing status of the analysis
		AnalysisStatus model.SonarAnalysisStatus `json:"analysis_status"`
	} `json:"data"`
}

// Analyze processes a SonarQube webhook payload and creates a complete analysis record.
// It performs concurrent validation, fetches project information, processes metrics,
// and calculates quality scores based on SonarQube quality gate conditions.
//
// The method implements the following workflow:
//  1. Concurrent project fetching and duplicate analysis checking
//  2. Branch validation against project configuration
//  3. Analysis processing with metrics collection
//  4. Quality score calculation and persistence
//
// Parameters:
//   - ctx: Context for the operation
//   - payload: SonarQube webhook payload containing analysis results
//   - options: Configuration options for analysis processing
//
// Returns the analysis response with score and status, or an error if processing fails.
func (s *SonarAnalizerInteractor) Analyze(ctx context.Context, payload SonarQubeWebhook, options AnalizeOptions) (AnalizeResponse, error) {
	slog.Debug("Received SonarQube Webhook", "payload", payload, "options", options)
	var response AnalizeResponse
	var wg sync.WaitGroup
	var fetchProjectErr error
	var analysisProcessedErr error
	var project model.SonarqubeProject

	// Perform concurrent validation operations for performance
	wg.Add(2)

	// Fetch project information concurrently
	go func() {
		defer wg.Done()
		project, fetchProjectErr = s.fetchProject(ctx, payload)
	}()

	// Check if analysis was already processed concurrently
	go func() {
		defer wg.Done()
		analysisProcessedErr = s.isAnalysisProcessed(ctx, payload.TaskID)
	}()

	wg.Wait()

	// Handle validation errors
	if fetchProjectErr != nil {
		return response, fetchProjectErr
	}

	if analysisProcessedErr != nil {
		return response, analysisProcessedErr
	}

	// Validate branch configuration
	err := s.validateBranch(payload.Branch, project)
	if err != nil {
		return response, err
	}

	// Process the complete analysis with metrics and scoring
	sonarAnalysis, err := s.processAnalysis(ctx, payload, project)
	if err != nil {
		return response, err
	}

	// Build response with analysis results
	response.Data.Score = sonarAnalysis.Score
	response.Data.AnalysisStatus = sonarAnalysis.Status
	response.Data.QualityGateStatus = sonarAnalysis.QualityGateStatus

	return response, nil
}

func (s *SonarAnalizerInteractor) validateBranch(branch SonarQubeWebhookBranch, project model.SonarqubeProject) error {
	branchName := strings.Trim(branch.Name, " ")

	if branch.Type != "BRANCH" {
		return fmt.Errorf("%w but got '%s'", ErrInvalidBranchType, branch.Type)
	}

	if branchName != project.Branch {
		return fmt.Errorf("%w: expected '%s' but got '%s'", ErrInvalidBranchName, project.Branch, branchName)
	}

	return nil
}

func (s *SonarAnalizerInteractor) processAnalysis(
	ctx context.Context,
	payload SonarQubeWebhook,
	project model.SonarqubeProject,
) (*model.SonarqubeAnalysis, error) {
	var wg sync.WaitGroup
	var measures sonarqube.GetMeasuresResponse
	var sonarMeasuresErr, sonarAnalysisErr, sonarIssuesErr error
	var sonarAnalysis *model.SonarqubeAnalysis
	var sonarIssuesResponse sonarqube.IssuesResponse

	wg.Add(3)

	go func() {
		defer wg.Done()

		measures, sonarMeasuresErr = s.fetchMeasures(ctx, payload)
	}()

	go func() {
		defer wg.Done()

		sonarAnalysis, sonarAnalysisErr = s.createSonarAnalysis(ctx, payload, project)
	}()

	go func() {
		defer wg.Done()

		sonarIssuesResponse, sonarIssuesErr = s.fetchIssues(ctx, payload)
	}()

	wg.Wait()

	if sonarAnalysisErr != nil {
		slog.Error("Error creating SonarQube Analysis", "error", sonarAnalysisErr)
		return nil, fmt.Errorf(core.ErrFormatWithCause, ErrCreateSonarAnalysis, sonarAnalysisErr)
	}

	if sonarMeasuresErr != nil {
		slog.Error("Error fetching SonarQube Measures", "error", sonarMeasuresErr)
		s.markAnalysisFailed(ctx, sonarAnalysis, sonarMeasuresErr)

		return nil, fmt.Errorf(core.ErrFormatWithCause, ErrFetchSonarMeasures, sonarMeasuresErr)
	}

	sonarAnalysis.Measures = map[string]any{
		"data": measures,
	}

	if sonarIssuesErr != nil {
		slog.Error("Error fetching SonarQube Issues", "error", sonarIssuesErr)
		s.markAnalysisFailed(ctx, sonarAnalysis, sonarIssuesErr)

		return nil, fmt.Errorf(core.ErrFormatWithCause, ErrFetchSonarIssues, sonarIssuesErr)
	}

	sonarAnalysis.IssuesTotal = sonarIssuesResponse.Total
	sonarAnalysis.IssuesEffortTotal = sonarIssuesResponse.EffortTotal

	return s.finalizeAnalysis(ctx, sonarAnalysis, payload)
}

func (s *SonarAnalizerInteractor) markAnalysisFailed(ctx context.Context, analysis *model.SonarqubeAnalysis, err error) {
	analysis.ErrorDetails = map[string]any{
		"data": err.Error(),
	}
	analysis.Status = model.SonarAnalysisFailed

	analysis.CompletedAt = time.Now().UTC()
	if saveErr := s.sonarqubeAnalysisRepo.Save(ctx, analysis, false); saveErr != nil {
		slog.Error("Error saving SonarQube Analysis", "error", saveErr)
	}
}

func (s *SonarAnalizerInteractor) finalizeAnalysis(
	ctx context.Context,
	analysis *model.SonarqubeAnalysis,
	payload SonarQubeWebhook,
) (*model.SonarqubeAnalysis, error) {
	score := s.calculateScore(ctx, payload)
	analysis.Score = score
	analysis.Status = model.SonarAnalysisSucceeded
	analysis.CompletedAt = time.Now().UTC()
	err := s.sonarqubeAnalysisRepo.Save(ctx, analysis, false)

	return analysis, err
}

func (s *SonarAnalizerInteractor) fetchIssues(_ context.Context, payload SonarQubeWebhook) (sonarqube.IssuesResponse, error) {
	filter := &sonarqube.IssueFilter{}
	pagination := &sonarqube.PaginationOptions{
		PageIndex: 1,
		PageSize:  1,
	}
	issues, err := s.sonarqube.GetProjectIssues(payload.Project.Key, filter, pagination)

	return issues, err
}

func (s *SonarAnalizerInteractor) fetchProject(ctx context.Context, payload SonarQubeWebhook) (model.SonarqubeProject, error) {
	var project model.SonarqubeProject
	// Get project from Database
	params := corerepository.PaginationParams[repository.SonarqubeProjectFilter]{
		Filters: repository.SonarqubeProjectFilter{
			ProjectKey: payload.Project.Key,
		},
		Page:     1,
		PageSize: 1,
	}

	projectsResp, err := s.sonarProjectRepo.List(ctx, params)
	if err != nil {
		return project, err
	}

	if len(projectsResp.Items) == 0 {
		return project, ErrProjectNotFound
	}

	project = projectsResp.Items[0]

	return project, nil
}

func (s *SonarAnalizerInteractor) fetchMeasures(_ context.Context, payload SonarQubeWebhook) (sonarqube.GetMeasuresResponse, error) {
	var measures sonarqube.GetMeasuresResponse
	params := sonarqube.GetMeasuresParams{
		Component: payload.Project.Key,
		//nolint:lll
		MetricKeys: "SMELL_COUNT_ABBREVIATIONS_USAGE,new_technical_debt,afferent-couplings,SMELL_COUNT_ANTI_PATTERN,SMELL_COUNT_BAD_DESIGN,SMELL_COUNT_BAD_FRAMEWORK_USAGE,SMELL_COUNT_BAD_LOGGING,blocker_violations,bugs,classes,code_smells,SMELL_DEBT,cognitive_complexity,comment_lines,comment_lines_data,comment_lines_density,class_complexity,file_complexity,function_complexity,complexity_in_classes,complexity_in_functions,branch_coverage,new_branch_coverage,conditions_to_cover,new_conditions_to_cover,confirmed_issues,coverage,new_coverage,critical_violations,critical_severity_vulns,complexity,last_commit_date,new_development_cost,directories,duplicated_blocks,new_duplicated_blocks,duplicated_files,duplicated_lines,duplicated_lines_density,new_duplicated_lines_density,new_duplicated_lines,duplications_data,efferent-couplings,effort_to_reach_maintainability_rating_a,executable_lines_data,false_positive_issues,file_complexity_distribution,files,function_complexity_distribution,functions,generated_lines,generated_ncloc,high_severity_vulns,SMELL_COUNT_HOW_COMMENT,SMELL_COUNT_INDECENT_EXPOSURE,info_violations,inherited_risk_score,violations,line_coverage,new_line_coverage,lines,ncloc,ncloc_language_distribution,lines_to_cover,new_lines_to_cover,low_severity_vulns,sqale_rating,new_maintainability_rating,major_violations,SMELL_COUNT_MEANINGLESS_COMMENT,medium_severity_vulns,SMELL_COUNT_MIDDLE_MAN,minor_violations,SMELL_COUNT_MISSING_DOCUMENTATION,SMELL_COUNT_MISSING_IMPLEMENTATION,missing_package_info_count,missing_package_info,SMELL_COUNT_MISSING_TEST,SMELL_COUNT_MULTIPLE_RESPONSIBILITIES,ncloc_data,new_blocker_violations,new_bugs,new_code_smells,new_critical_violations,new_info_violations,new_violations,new_lines,new_major_violations,new_minor_violations,new_security_hotspots,new_vulnerabilities,SMELL_COUNT_NON_COMPLIANCE_WITH_STANDARDS,SMELL_COUNT_NON_EXCEPTION,number-of-classes-and-interfaces,java_count,package_info_count,package_count,package,unanalyzed_c,unanalyzed_cpp,SMELL_COUNT_ODDBALL_SOLUTION,open_issues,SMELL_COUNT_OVERCOMPLICATED_ALGORITHM,package-dependency-cycles,SMELL_COUNT_PRIMITIVES_OBSESSION,quality_profiles,projects,public_api,public_documented_api_density,public_undocumented_api,quality_gate_details,alert_status,SMELL_COUNT_REFUSED_BEQUEST,SMELL_COUNT_REINVENTED_WHEEL,reliability_rating,new_reliability_rating,reliability_remediation_effort,new_reliability_remediation_effort,reopened_issues,security_hotspots,security_hotspots_reviewed,new_security_hotspots_reviewed,security_rating,new_security_rating,security_remediation_effort,new_security_remediation_effort,security_review_rating,new_security_review_rating,security_hotspots_reviewed_status,new_security_hotspots_reviewed_status,security_hotspots_to_review_status,new_security_hotspots_to_review_status,skipped_tests,SMELL_COUNT,SMELL_COUNT_SOLUTION_SPRAWL,SMELL_COUNT_SPECULATIVE_GENERALITY,statements,sqale_index,sqale_debt_ratio,new_sqale_debt_ratio,total_dependencies,total_vulnerabilities,SMELL_COUNT_OTHER,SMELL_COUNT_UNCOMMUNICATIVE_NAME,uncovered_conditions,new_uncovered_conditions,uncovered_lines,new_uncovered_lines,test_execution_time,test_errors,test_failures,tests,test_success_density,SMELL_COUNT_USELESS_TEST,vulnerabilities,vulnerable_component_ratio,vulnerable_dependencies,wont_fix_issues,SMELL_COUNT_WRONG_LANGUAGE,SMELL_COUNT_WRONG_LOGIC",
	}

	measures, err := s.sonarqube.GetMeasures(params)
	if err != nil {
		return measures, err
	}

	return measures, nil
}

func (s *SonarAnalizerInteractor) isAnalysisProcessed(ctx context.Context, taskID string) error {
	analyses, err := s.sonarqubeAnalysisRepo.List(ctx, corerepository.PaginationParams[repository.SonarqubeAnalysisFilter]{
		Filters: repository.SonarqubeAnalysisFilter{
			AnalysisID: taskID,
		},
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		return err
	}

	if len(analyses.Items) > 0 {
		return fmt.Errorf("%w: task ID '%s'", ErrAnalysisAlreadyProcessed, taskID)
	}

	return nil
}

func (s *SonarAnalizerInteractor) createSonarAnalysis(ctx context.Context,
	payload SonarQubeWebhook,
	project model.SonarqubeProject,
) (*model.SonarqubeAnalysis, error) {
	var measures sonarqube.GetMeasuresResponse
	var sqTime sonarqube.SonarQubeTime

	err := json.Unmarshal([]byte(`"`+payload.AnalysedAt+`"`), &sqTime)
	if err != nil {
		return nil, err
	}

	sonarAnalysis := &model.SonarqubeAnalysis{
		SonarqubeProjectID: project.ID,
		AnalysisID:         payload.TaskID,
		WebhookPayload: map[string]any{
			"data": payload,
		},
		Measures: map[string]any{
			"data": measures,
		},
		Status:            model.SonarAnalysisProcessing,
		Score:             0,
		QualityGateName:   payload.QualityGate.Name,
		QualityGateStatus: payload.QualityGate.Status,
		AnalysedAt:        sqTime.Time,
	}

	err = s.sonarqubeAnalysisRepo.Save(ctx, sonarAnalysis, false)
	if err != nil {
		return nil, err
	}

	return sonarAnalysis, nil
}

func (s *SonarAnalizerInteractor) calculateScore(_ context.Context, payload SonarQubeWebhook) float64 {
	var score float64 = 100
	penaltyMap := map[string]float64{
		"new_security_rating":          7.63,
		"new_reliability_rating":       6.87,
		"security_rating":              6.87,
		"critical_severity_vulns":      6.87,
		"new_security_hotspots":        6.23,
		"new_coverage":                 6.11,
		"reliability_rating":           6.11,
		"new_critical_violations":      6.11,
		"critical_violations":          5.35,
		"blocker_violations":           5.35,
		"high_severity_vulns":          5.35,
		"new_maintainability_rating":   4.59,
		"sqale_rating":                 4.59,
		"coverage":                     4.59,
		"new_duplicated_lines_density": 3.82,
		"medium_severity_vulns":        3.82,
		"new_major_violations":         3.82,
		"major_violations":             3.06,
		"duplicated_blocks":            3.06,
		"comment_lines_density":        2.29,
		"reopened_issues":              2.29,
		"low_severity_vulns":           1.53,
	}

	for _, measure := range payload.QualityGate.Conditions {
		metric := measure.Metric
		status := strings.ToLower(measure.Status)

		// operator := measure.Operator
		// value := measure.Value
		// status := measure.Status
		// errorThreshold := measure.ErrorThreshold
		if penalty, ok := penaltyMap[metric]; ok && status == "error" {
			score -= penalty
		}
	}

	return score
}
