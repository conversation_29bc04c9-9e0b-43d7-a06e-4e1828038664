package interactor

import (
	"context"
	"errors"
	"fmt"

	corerepository "sa-intranet/core/repository"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

// CompanyClientsData represents the response structure for company client listings.
// It includes both the client data and pagination metadata.
type CompanyClientsData struct {
	// CompanyClients contains the list of company clients for the current page
	CompanyClients []CompanyClient `json:"companyClients"`
	// Pagination contains metadata about the current page and total count
	Pagination Pagination `json:"pagination"`
}

// CompanyClient represents a company client entity with basic information.
type CompanyClient struct {
	// ID is the unique identifier for the company client
	ID uuid.UUID `json:"id"`
	// Name is the company client name
	Name string `json:"name"`
}

// CompanyClientsInteractor handles business logic for company client management.
// It provides operations for creating, listing, and managing company clients.
type CompanyClientsInteractor struct {
	// CompanyClientRepo provides data access for company clients
	CompanyClientRepo repository.CompanyClientRepository
	// validator provides input validation capabilities
	validator *validator.Validate
}

// CompanyClientValidator defines validation rules for company client input.
type CompanyClientValidator struct {
	// Name is required for company client creation
	Name string `json:"name" validate:"required"`
}

func NewCompanyClientsInteractor(i *do.Injector) (*CompanyClientsInteractor, error) {
	CompanyClientRepo, err := do.Invoke[repository.CompanyClientRepository](i)
	if err != nil {
		return nil, err
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, err
	}

	return &CompanyClientsInteractor{
		CompanyClientRepo: CompanyClientRepo,
		validator:         validator,
	}, nil
}

func (i *CompanyClientsInteractor) InitialData(filter string, page int, pageSize int) (CompanyClientsData, error) {
	var data CompanyClientsData
	ctx := context.Background()

	params := corerepository.PaginationParams[repository.CompanyClientFilter]{
		Filters: repository.CompanyClientFilter{
			Name: filter,
		},
		Page:     page,
		PageSize: pageSize,
	}

	companyClientsResp, err := i.CompanyClientRepo.List(ctx, params)
	if err != nil {
		return data, err
	}

	companyClients := make([]CompanyClient, 0, len(companyClientsResp.Items))

	for _, client := range companyClientsResp.Items {
		companyClients = append(companyClients, CompanyClient{
			ID:   client.ID,
			Name: client.Name,
		})
	}

	pagination := Pagination{
		TotalCount:  int(companyClientsResp.TotalItems),
		CurrentPage: companyClientsResp.Page,
		PerPage:     companyClientsResp.PageSize,
	}

	return CompanyClientsData{
		Pagination:     pagination,
		CompanyClients: companyClients,
	}, nil
}

func (i *CompanyClientsInteractor) CreateCompanyClient(companyClient CompanyClientValidator) (CompanyClient, map[string]any, error) {
	var validationErrors map[string]any
	companyClientResp := CompanyClient{}

	err := i.validator.Struct(companyClient)
	if err != nil {
		var validationErr validator.ValidationErrors

		ok := errors.As(err, &validationErr)
		if !ok {
			return companyClientResp, nil, fmt.Errorf("failed to validate company client: %w", err)
		}

		validationErrors = validatorext.FormatFormErrors(companyClient, validationErr)

		return companyClientResp, validationErrors, ErrValidationFailed
	}

	client := &model.CompanyClient{
		Name: companyClient.Name,
	}

	companyClientDB, saveErr := i.CompanyClientRepo.Save(context.Background(), client)
	if saveErr != nil {
		return companyClientResp, nil, saveErr
	}

	companyClientResp = CompanyClient{
		ID:   companyClientDB.ID,
		Name: companyClientDB.Name,
	}

	return companyClientResp, nil, nil
}

func (i *CompanyClientsInteractor) UpdateCompanyClient(companyclientID string, companyClient CompanyClientValidator) (CompanyClient, map[string]any, error) {
	var validationErrors map[string]any
	ctx := context.Background()
	companyClientResp := CompanyClient{}

	clientUUID, err := uuid.Parse(companyclientID)
	if err != nil {
		return companyClientResp, nil, err
	}

	companyClientFromDB, err := i.CompanyClientRepo.Find(ctx, clientUUID)
	if err != nil {
		return companyClientResp, nil, err
	}

	err = i.validator.Struct(companyClient)
	if err != nil {
		var validationErr validator.ValidationErrors

		ok := errors.As(err, &validationErr)
		if !ok {
			return companyClientResp, nil, fmt.Errorf("failed to validate company client: %w", err)
		}

		validationErrors = validatorext.FormatFormErrors(companyClient, validationErr)

		return companyClientResp, validationErrors, ErrValidationFailed
	}

	client := &model.CompanyClient{
		ID:        companyClientFromDB.ID,
		Name:      companyClient.Name,
		CreatedAt: companyClientFromDB.CreatedAt,
	}

	client, saveErr := i.CompanyClientRepo.Save(context.Background(), client)
	if saveErr != nil {
		return companyClientResp, nil, saveErr
	}

	companyClientResp = CompanyClient{
		ID:   client.ID,
		Name: client.Name,
	}

	return companyClientResp, nil, nil
}
