package sonarqube_test

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	intranetConfig "sa-intranet/config"
	"sa-intranet/usecase/cqm/sonarqube"

	intranet "sa-intranet"

	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	// Set up logging for tests
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	slog.SetDefault(logger)

	injector = do.New()

	conf, err := intranetConfig.NewConfig()
	if err != nil {
		panic(err)
	}

	if err = intranet.Register(injector, *conf); err != nil {
		panic(err)
	}

	// Run all tests
	m.Run()
}

func TestClient_GetProjectIssues(t *testing.T) {
	t.Parallel()

	ts := setupTestServer(t)
	defer ts.Close()

	client := setupTestClient(ts.URL)
	response := executeTest(t, client)
	verifyResponse(t, response)
}

func setupTestServer(t *testing.T) *httptest.Server {
	t.Helper()

	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		verifyRequest(t, r)

		mockResponse := createMockResponse()

		w.Header().Set("Content-Type", "application/json")

		err := json.NewEncoder(w).Encode(mockResponse)
		if err != nil {
			t.Fatalf("Failed to encode response: %v", err)
		}
	}))
}

func verifyRequest(t *testing.T, r *http.Request) {
	t.Helper()

	if r.URL.Path != "/api/issues/search" {
		t.Errorf("Expected request to '/api/issues/search', got '%s'", r.URL.Path)
	}

	if r.Method != "GET" {
		t.Errorf("Expected GET request, got %s", r.Method)
	}

	query := r.URL.Query()

	verifyQueryParams(t, query)
}

func verifyQueryParams(t *testing.T, query map[string][]string) {
	t.Helper()

	projectKey := query["componentKeys"][0]
	if projectKey != "test-project" {
		t.Errorf("Expected componentKeys=test-project, got %s", projectKey)
	}

	pageSize := query["ps"][0]
	if pageSize != "50" {
		t.Errorf("Expected ps=50, got %s", pageSize)
	}

	pageIndex := query["p"][0]
	if pageIndex != "2" {
		t.Errorf("Expected p=2, got %s", pageIndex)
	}

	severities := query["severities"][0]
	if severities != "MAJOR,CRITICAL" {
		t.Errorf("Expected severities=MAJOR,CRITICAL, got %s", severities)
	}
}

func createMockResponse() sonarqube.IssuesResponse {
	return sonarqube.IssuesResponse{
		Total:       150,
		P:           2,
		Ps:          50,
		EffortTotal: 240,
		Issues: []sonarqube.Issue{
			{
				Key:       "issue-1",
				Rule:      "rule-1",
				Severity:  "MAJOR",
				Component: "test-project:src/main.go",
				Line:      42,
				Status:    "OPEN",
			},
			{
				Key:       "issue-2",
				Rule:      "rule-2",
				Severity:  "CRITICAL",
				Component: "test-project:src/utils.go",
				Line:      15,
				Status:    "OPEN",
			},
		},
		Components: []sonarqube.Component{
			{
				Key:  "test-project:src/main.go",
				Name: "main.go",
				Path: "src/main.go",
			},
			{
				Key:  "test-project:src/utils.go",
				Name: "utils.go",
				Path: "src/utils.go",
			},
		},
		Paging: struct {
			PageIndex int `json:"pageIndex"`
			PageSize  int `json:"pageSize"`
			Total     int `json:"total"`
		}{
			PageIndex: 2,
			PageSize:  50,
			Total:     150,
		},
	}
}

func setupTestClient(serverURL string) *sonarqube.Client {
	i := injector.Clone()
	do.Override(i, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		config := sonarqube.ClientConfig{
			URL:   serverURL,
			Token: "test-token",
		}

		return config, nil
	})

	return do.MustInvoke[*sonarqube.Client](i)
}

func executeTest(t *testing.T, client *sonarqube.Client) *sonarqube.IssuesResponse {
	t.Helper()

	filter := &sonarqube.IssueFilter{
		Severities: []string{"MAJOR", "CRITICAL"},
	}

	pagination := &sonarqube.PaginationOptions{
		PageIndex: 2,
		PageSize:  50,
	}

	response, err := client.GetProjectIssues("test-project", filter, pagination)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	return &response
}

func verifyResponse(t *testing.T, response *sonarqube.IssuesResponse) {
	t.Helper()

	if response.Total != 150 {
		t.Errorf("Expected total=150, got %d", response.Total)
	}

	if len(response.Issues) != 2 {
		t.Errorf("Expected 2 issues, got %d", len(response.Issues))
	}

	if response.Issues[0].Key != "issue-1" {
		t.Errorf("Expected first issue key to be 'issue-1', got '%s'", response.Issues[0].Key)
	}

	if response.Issues[1].Severity != "CRITICAL" {
		t.Errorf("Expected second issue severity to be 'CRITICAL', got '%s'", response.Issues[1].Severity)
	}

	if response.Paging.Total != 150 {
		t.Errorf("Expected paging total=150, got %d", response.Paging.Total)
	}
}

func setupTestGetAllServer(t *testing.T) (*httptest.Server, *int) {
	t.Helper()

	pageCount := 0
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/api/issues/search" {
			t.Errorf("Expected request to '/api/issues/search', got '%s'", r.URL.Path)
		}

		query := r.URL.Query()
		pageIndex := query.Get("p")
		pageCount++

		var mockResponse sonarqube.IssuesResponse
		totalIssues := 6
		pageSize := 2

		currentPage, _ := strconv.Atoi(pageIndex)
		startIdx := (currentPage - 1) * pageSize

		endIdx := startIdx + pageSize
		if endIdx > totalIssues {
			endIdx = totalIssues
		}

		var issues []sonarqube.Issue
		for i := startIdx; i < endIdx; i++ {
			issues = append(issues, sonarqube.Issue{
				Key:       fmt.Sprintf("issue-%d", i+1),
				Severity:  "MAJOR",
				Component: "test-project:src/file.go",
				Status:    "OPEN",
			})
		}

		mockResponse = sonarqube.IssuesResponse{
			Total:  totalIssues,
			P:      currentPage,
			Ps:     pageSize,
			Issues: issues,
			Paging: struct {
				PageIndex int `json:"pageIndex"`
				PageSize  int `json:"pageSize"`
				Total     int `json:"total"`
			}{
				PageIndex: currentPage,
				PageSize:  pageSize,
				Total:     totalIssues,
			},
		}

		w.Header().Set("Content-Type", "application/json")

		err := json.NewEncoder(w).Encode(mockResponse)
		if err != nil {
			t.Fatalf("Failed to encode response: %v", err)
		}
	}))

	return ts, &pageCount
}

func TestClient_GetAllProjectIssues(t *testing.T) {
	t.Parallel()

	ts, pageCount := setupTestGetAllServer(t)
	defer ts.Close()

	i := injector.Clone()
	do.Override(i, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		config := sonarqube.ClientConfig{
			URL:   ts.URL,
			Token: "test-token",
		}

		return config, nil
	})

	client := do.MustInvoke[*sonarqube.Client](i)

	issues, err := client.GetAllProjectIssues("test-project", nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(issues) != 6 {
		t.Errorf("Expected 6 issues, got %d", len(issues))
	}

	if *pageCount != 3 {
		t.Errorf("Expected 3 API calls, got %d", *pageCount)
	}

	for i, issue := range issues {
		expectedKey := fmt.Sprintf("issue-%d", i+1)
		if issue.Key != expectedKey {
			t.Errorf("Expected issue key %s, got %s", expectedKey, issue.Key)
		}
	}
}

func TestClient_GetMeasures(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		params         sonarqube.GetMeasuresParams
		serverResponse string
		serverStatus   int
		wantErr        bool
		verify         func(t *testing.T, response sonarqube.GetMeasuresResponse, err error)
	}{
		{
			name: "successful_get_measures",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "coverage,bugs,vulnerabilities",
			},
			serverResponse: `{
				"component": {
					"key": "test-project",
					"name": "Test Project",
					"measures": [
						{
							"metric": "coverage",
							"value": "85.5"
						},
						{
							"metric": "bugs",
							"value": "3"
						}
					]
				}
			}`,
			serverStatus: http.StatusOK,
			wantErr:      false,
			verify: func(t *testing.T, response sonarqube.GetMeasuresResponse, err error) {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
				if response.Component.Key != "test-project" {
					t.Errorf("Expected component key 'test-project', got %s", response.Component.Key)
				}
				if len(response.Component.Measures) != 2 {
					t.Errorf("Expected 2 measures, got %d", len(response.Component.Measures))
				}
			},
		},
		{
			name: "empty_component_parameter",
			params: sonarqube.GetMeasuresParams{
				Component:  "",
				MetricKeys: "coverage,bugs",
			},
			serverResponse: "",
			serverStatus:   http.StatusOK,
			wantErr:        true,
			verify: func(t *testing.T, response sonarqube.GetMeasuresResponse, err error) {
				if err == nil {
					t.Error("Expected error for empty component parameter")
				}
				if err != nil && !strings.Contains(err.Error(), "component") {
					t.Errorf("Expected error to mention 'component', got %v", err)
				}
			},
		},
		{
			name: "empty_metric_keys_parameter",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "",
			},
			serverResponse: "",
			serverStatus:   http.StatusOK,
			wantErr:        true,
			verify: func(t *testing.T, response sonarqube.GetMeasuresResponse, err error) {
				if err == nil {
					t.Error("Expected error for empty metricKeys parameter")
				}
				if err != nil && !strings.Contains(err.Error(), "metricKeys") {
					t.Errorf("Expected error to mention 'metricKeys', got %v", err)
				}
			},
		},
		{
			name: "server_error_response",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "coverage",
			},
			serverResponse: `{"error": "Project not found"}`,
			serverStatus:   http.StatusNotFound,
			wantErr:        true,
			verify: func(t *testing.T, response sonarqube.GetMeasuresResponse, err error) {
				if err == nil {
					t.Error("Expected error for server error response")
				}
			},
		},
		{
			name: "invalid_json_response",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "coverage",
			},
			serverResponse: `{"invalid": json}`,
			serverStatus:   http.StatusOK,
			wantErr:        true,
			verify: func(t *testing.T, response sonarqube.GetMeasuresResponse, err error) {
				if err == nil {
					t.Error("Expected error for invalid JSON response")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request parameters
				if tt.params.Component != "" {
					component := r.URL.Query().Get("component")
					if component != tt.params.Component {
						t.Errorf("Expected component %s, got %s", tt.params.Component, component)
					}
				}

				if tt.params.MetricKeys != "" {
					metricKeys := r.URL.Query().Get("metricKeys")
					if metricKeys != tt.params.MetricKeys {
						t.Errorf("Expected metricKeys %s, got %s", tt.params.MetricKeys, metricKeys)
					}
				}

				w.WriteHeader(tt.serverStatus)
				w.Write([]byte(tt.serverResponse))
			}))
			defer ts.Close()

			// Create client with test server URL
			client := setupTestClient(ts.URL)

			// Execute test
			response, err := client.GetMeasures(tt.params)

			// Verify results
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMeasures() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.verify != nil {
				tt.verify(t, response, err)
			}
		})
	}
}

func TestClient_ValidateGetMeasures(t *testing.T) {
	t.Parallel()

	// Create a client for testing
	client := setupTestClient("http://test.example.com")

	tests := []struct {
		name    string
		params  sonarqube.GetMeasuresParams
		wantErr bool
		verify  func(t *testing.T, err error)
	}{
		{
			name: "valid_parameters",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "coverage,bugs,vulnerabilities",
			},
			wantErr: false,
			verify: func(t *testing.T, err error) {
				if err != nil {
					t.Errorf("Expected no error for valid parameters, got %v", err)
				}
			},
		},
		{
			name: "empty_component",
			params: sonarqube.GetMeasuresParams{
				Component:  "",
				MetricKeys: "coverage,bugs",
			},
			wantErr: true,
			verify: func(t *testing.T, err error) {
				if err == nil {
					t.Error("Expected error for empty component")
				}
				if err != nil && !strings.Contains(err.Error(), "component") {
					t.Errorf("Expected error to mention 'component', got %v", err)
				}
			},
		},
		{
			name: "empty_metric_keys",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "",
			},
			wantErr: true,
			verify: func(t *testing.T, err error) {
				if err == nil {
					t.Error("Expected error for empty metricKeys")
				}
				if err != nil && !strings.Contains(err.Error(), "metricKeys") {
					t.Errorf("Expected error to mention 'metricKeys', got %v", err)
				}
			},
		},
		{
			name: "whitespace_only_component",
			params: sonarqube.GetMeasuresParams{
				Component:  "   \t\n   ",
				MetricKeys: "coverage",
			},
			wantErr: false, // The validation only checks for empty string, not whitespace
			verify: func(t *testing.T, err error) {
				// This should pass validation since it's not an empty string
				if err != nil {
					t.Errorf("Expected no error for whitespace component, got %v", err)
				}
			},
		},
		{
			name: "whitespace_only_metric_keys",
			params: sonarqube.GetMeasuresParams{
				Component:  "test-project",
				MetricKeys: "   \t\n   ",
			},
			wantErr: false, // The validation only checks for empty string, not whitespace
			verify: func(t *testing.T, err error) {
				// This should pass validation since it's not an empty string
				if err != nil {
					t.Errorf("Expected no error for whitespace metricKeys, got %v", err)
				}
			},
		},
		{
			name: "both_parameters_empty",
			params: sonarqube.GetMeasuresParams{
				Component:  "",
				MetricKeys: "",
			},
			wantErr: true,
			verify: func(t *testing.T, err error) {
				if err == nil {
					t.Error("Expected error for both empty parameters")
				}
				// Should fail on the first validation (component)
				if err != nil && !strings.Contains(err.Error(), "component") {
					t.Errorf("Expected error to mention 'component' first, got %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := client.ValidateGetMeasures(tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateGetMeasures() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.verify != nil {
				tt.verify(t, err)
			}
		})
	}
}

func TestClient_GetProject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		projectKey     string
		serverResponse string
		serverStatus   int
		wantErr        bool
		verify         func(t *testing.T, component sonarqube.Component, err error)
	}{
		{
			name:       "successful_get_project",
			projectKey: "test-project",
			serverResponse: `{
				"components": [
					{
						"key": "test-project",
						"name": "Test Project",
						"qualifier": "TRK"
					}
				]
			}`,
			serverStatus: http.StatusOK,
			wantErr:      false,
			verify: func(t *testing.T, component sonarqube.Component, err error) {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
				if component.Key != "test-project" {
					t.Errorf("Expected component key 'test-project', got %s", component.Key)
				}
				if component.Name != "Test Project" {
					t.Errorf("Expected component name 'Test Project', got %s", component.Name)
				}
			},
		},
		{
			name:       "project_not_found",
			projectKey: "non-existent-project",
			serverResponse: `{
				"components": []
			}`,
			serverStatus: http.StatusOK,
			wantErr:      true,
			verify: func(t *testing.T, component sonarqube.Component, err error) {
				if err == nil {
					t.Error("Expected error for project not found")
				}
				if err != nil && !strings.Contains(err.Error(), "no project found") {
					t.Errorf("Expected error to mention 'no project found', got %v", err)
				}
			},
		},
		{
			name:           "server_error_response",
			projectKey:     "test-project",
			serverResponse: `{"error": "Internal server error"}`,
			serverStatus:   http.StatusInternalServerError,
			wantErr:        true,
			verify: func(t *testing.T, component sonarqube.Component, err error) {
				if err == nil {
					t.Error("Expected error for server error response")
				}
				if err != nil && !strings.Contains(err.Error(), "status 500") {
					t.Errorf("Expected error to mention status code, got %v", err)
				}
			},
		},
		{
			name:           "invalid_json_response",
			projectKey:     "test-project",
			serverResponse: `{"invalid": json}`,
			serverStatus:   http.StatusOK,
			wantErr:        true,
			verify: func(t *testing.T, component sonarqube.Component, err error) {
				if err == nil {
					t.Error("Expected error for invalid JSON response")
				}
			},
		},
		{
			name:       "multiple_projects_returns_first",
			projectKey: "test-project",
			serverResponse: `{
				"components": [
					{
						"key": "test-project-1",
						"name": "Test Project 1",
						"qualifier": "TRK"
					},
					{
						"key": "test-project-2",
						"name": "Test Project 2",
						"qualifier": "TRK"
					}
				]
			}`,
			serverStatus: http.StatusOK,
			wantErr:      false,
			verify: func(t *testing.T, component sonarqube.Component, err error) {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
				if component.Key != "test-project-1" {
					t.Errorf("Expected first component key 'test-project-1', got %s", component.Key)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request parameters
				q := r.URL.Query().Get("q")
				if q != tt.projectKey {
					t.Errorf("Expected q parameter %s, got %s", tt.projectKey, q)
				}

				qualifiers := r.URL.Query().Get("qualifiers")
				if qualifiers != "TRK" {
					t.Errorf("Expected qualifiers parameter 'TRK', got %s", qualifiers)
				}

				w.WriteHeader(tt.serverStatus)
				w.Write([]byte(tt.serverResponse))
			}))
			defer ts.Close()

			// Create client with test server URL
			client := setupTestClient(ts.URL)

			// Execute test
			component, err := client.GetProject(tt.projectKey)

			// Verify results
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProject() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.verify != nil {
				tt.verify(t, component, err)
			}
		})
	}
}

func TestSonarQubeCustomTime(t *testing.T) {
	// Test cases for different time formats
	testCases := []struct {
		name     string
		jsonTime string
		expected time.Time
		hasError bool
	}{
		{
			name:     "Format with timezone offset -0700",
			jsonTime: `"2023-01-02T15:04:05-0700"`,
			expected: time.Date(2023, 1, 2, 15, 4, 5, 0, time.FixedZone("", -7*60*60)),
			hasError: false,
		},
		{
			name:     "Format with +0000",
			jsonTime: `"2025-03-24T15:05:32+0000"`,
			expected: time.Date(2025, 3, 24, 15, 5, 32, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "Format with Z",
			jsonTime: `"2023-01-02T15:04:05Z"`,
			expected: time.Date(2023, 1, 2, 15, 4, 5, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "Null value",
			jsonTime: `null`,
			expected: time.Time{},
			hasError: false,
		},
		{
			name:     "Invalid format",
			jsonTime: `"2023/01/02 15:04:05"`,
			expected: time.Time{},
			hasError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			var sqTime sonarqube.SonarQubeTime
			err := json.Unmarshal([]byte(tc.jsonTime), &sqTime)

			if tc.hasError {
				if err == nil {
					t.Errorf("Expected error but got none for %s", tc.name)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for %s: %v", tc.name, err)
				}

				if !sqTime.Time.UTC().Equal(tc.expected.UTC()) {
					t.Errorf("Expected time %v but got %v for %s",
						tc.expected.UTC(), sqTime.UTC(), tc.name)
				}
			}
		})
	}
}
