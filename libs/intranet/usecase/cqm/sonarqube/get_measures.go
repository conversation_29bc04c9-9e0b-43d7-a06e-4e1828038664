// Package sonarqube provides SonarQube measures API functionality.
// This file contains types and functions for retrieving project measures and metrics.
package sonarqube

import (
	"fmt"
	"net/url"
)

// Error definitions for SonarQube measures operations
var (
	// ErrMissingRequiredParameter is returned when required API parameters are missing
	ErrMissingRequiredParameter = fmt.Errorf("missing required parameter")
	// ErrInvalidSonarQubeURL is returned when the SonarQube URL is malformed
	ErrInvalidSonarQubeURL = fmt.E<PERSON><PERSON>("invalid SonarQube URL")
)

// GetMeasuresEndpoint defines the API endpoint for retrieving component measures
const GetMeasuresEndpoint = "api/measures/component"

// GetMeasuresParams represents the parameters for the SonarQube measures API.
// It defines the query parameters needed to retrieve specific metrics for a component.
type GetMeasuresParams struct {
	// AdditionalFields specifies additional fields to include in the response
	AdditionalFields string `url:"additionalFields,omitempty"`
	// Branch specifies the branch to analyze (for branch analysis)
	Branch string `url:"branch,omitempty"`
	// Component is the component key to retrieve measures for (required)
	Component string `url:"component"`
	// MetricKeys is a comma-separated list of metric keys to retrieve (required)
	MetricKeys string `url:"metricKeys"`
}

type GetMeasuresResponse struct {
	Component struct {
		Key       string `json:"key"`
		Name      string `json:"name"`
		Qualifier string `json:"qualifier"`
		Measures  []struct {
			Metric string `json:"metric"`
			Value  string `json:"value"`
		} `json:"measures"`
	} `json:"component"`
}

func (c *Client) GetMeasures(params GetMeasuresParams) (GetMeasuresResponse, error) {
	var response GetMeasuresResponse

	baseURL, err := url.Parse(c.config.URL)
	if err != nil {
		return response, fmt.Errorf("%w: %w", ErrInvalidSonarQubeURL, err)
	}

	// Use a different variable name to avoid shadowing
	if validationErr := c.ValidateGetMeasures(params); validationErr != nil {
		return response, validationErr
	}

	baseURL.Path = GetMeasuresEndpoint

	requestParams := url.Values{}
	requestParams.Add("component", params.Component)
	requestParams.Add("metricKeys", params.MetricKeys)
	baseURL.RawQuery = requestParams.Encode()

	response, err = doRequest[GetMeasuresResponse](c, "GET", baseURL.String(), nil)
	if err != nil {
		return response, fmt.Errorf("failed to get measures: %w", err)
	}

	return response, nil
}

func (c *Client) ValidateGetMeasures(params GetMeasuresParams) error {
	if params.Component == "" {
		return fmt.Errorf("%s: %w", "component", ErrMissingRequiredParameter)
	}

	if params.MetricKeys == "" {
		return fmt.Errorf("%s: %w", "metricKeys", ErrMissingRequiredParameter)
	}

	return nil
}
