package jira

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"sa-intranet/http/client"

	"github.com/samber/do"
)

func TestClientTimeout(t *testing.T) {
	expected := 30 * time.Second
	if ClientTimeout != expected {
		t.<PERSON>rf("ClientTimeout = %v, want %v", ClientTimeout, expected)
	}
}

func TestErrJiraAPI(t *testing.T) {
	if ErrJiraAPI == nil {
		t.Error("ErrJiraAPI should not be nil")
	}

	expected := "jira API error"
	if ErrJiraAPI.Error() != expected {
		t.Errorf("ErrJiraAPI.Error() = %v, want %v", ErrJiraAPI.Error(), expected)
	}
}

func TestErrJiraProjectNotFound(t *testing.T) {
	if ErrJiraProjectNotFound == nil {
		t.Error("ErrJiraProjectNotFound should not be nil")
	}

	expected := "project not found, please check the project key or token"
	if ErrJiraProjectNotFound.Error() != expected {
		t.<PERSON>("ErrJiraProjectNotFound.Error() = %v, want %v", ErrJiraProjectNotFound.Error(), expected)
	}
}

func TestClientConfig(t *testing.T) {
	config := ClientConfig{}
	// Test that ClientConfig can be instantiated
	_ = config
}

func TestCredentials(t *testing.T) {
	tests := []struct {
		name     string
		creds    Credentials
		username string
		token    string
	}{
		{
			name: "credentials with username and token",
			creds: Credentials{
				Username: "testuser",
				Token:    "testtoken",
			},
			username: "testuser",
			token:    "testtoken",
		},
		{
			name: "empty credentials",
			creds: Credentials{
				Username: "",
				Token:    "",
			},
			username: "",
			token:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.creds.Username != tt.username {
				t.Errorf("Credentials.Username = %v, want %v", tt.creds.Username, tt.username)
			}
			if tt.creds.Token != tt.token {
				t.Errorf("Credentials.Token = %v, want %v", tt.creds.Token, tt.token)
			}
		})
	}
}

func TestClient(t *testing.T) {
	tests := []struct {
		name         string
		client       *Client
		expectClient bool
		expectConfig bool
	}{
		{
			name: "client with default client and config",
			client: &Client{
				DefaultClient: &client.DefaultClient{
					Client: &http.Client{Timeout: ClientTimeout},
				},
				config: ClientConfig{},
			},
			expectClient: true,
			expectConfig: true,
		},
		{
			name: "client with nil default client",
			client: &Client{
				DefaultClient: nil,
				config:        ClientConfig{},
			},
			expectClient: false,
			expectConfig: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectClient && tt.client.DefaultClient == nil {
				t.Error("Client.DefaultClient should not be nil")
			}
			if !tt.expectClient && tt.client.DefaultClient != nil {
				t.Error("Client.DefaultClient should be nil")
			}
		})
	}
}

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
		verify  func(t *testing.T, client *Client, injector *do.Injector)
	}{
		{
			name: "successful client creation",
			setup: func() *do.Injector {
				injector := do.New()
				do.Provide(injector, func(i *do.Injector) (ClientConfig, error) {
					return ClientConfig{}, nil
				})
				return injector
			},
			wantErr: false,
			verify: func(t *testing.T, client *Client, injector *do.Injector) {
				if client == nil {
					t.Error("New() returned nil client")
					return
				}
				if client.DefaultClient == nil {
					t.Error("New() client.DefaultClient is nil")
				}
				if client.DefaultClient.Client == nil {
					t.Error("New() client.DefaultClient.Client is nil")
				}
				if client.DefaultClient.Client.Timeout != ClientTimeout {
					t.Errorf("New() client timeout = %v, want %v", client.DefaultClient.Client.Timeout, ClientTimeout)
				}
			},
		},
		{
			name: "missing config dependency",
			setup: func() *do.Injector {
				return do.New() // No ClientConfig provided
			},
			wantErr: true,
			verify:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			injector := tt.setup()

			client, err := New(injector)

			if tt.wantErr {
				if err == nil {
					t.Error("New() expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("New() unexpected error = %v", err)
				return
			}

			if tt.verify != nil {
				tt.verify(t, client, injector)
			}
		})
	}
}

// Test data structures
func TestCreateIssueRequest(t *testing.T) {
	tests := []struct {
		name    string
		request CreateIssueRequest
		verify  func(t *testing.T, request CreateIssueRequest)
	}{
		{
			name: "create issue request with all fields",
			request: CreateIssueRequest{
				Fields: IssueFields{
					Project: ProjectField{
						ID: "10001",
					},
					IssueType: IssueTypeField{
						ID: "10004",
					},
					Summary: "Test Issue",
					Description: Description{
						Type:    "doc",
						Version: 1,
						Content: []ContentContainer{
							{
								Type: "paragraph",
								Content: []Content{
									{
										Type: "text",
										Text: "Test description",
									},
								},
							},
						},
					},
					Labels: []string{"test", "automation"},
				},
			},
			verify: func(t *testing.T, request CreateIssueRequest) {
				if request.Fields.Project.ID != "10001" {
					t.Errorf("Project.ID = %v, want %v", request.Fields.Project.ID, "10001")
				}
				if request.Fields.IssueType.ID != "10004" {
					t.Errorf("IssueType.ID = %v, want %v", request.Fields.IssueType.ID, "10004")
				}
				if request.Fields.Summary != "Test Issue" {
					t.Errorf("Summary = %v, want %v", request.Fields.Summary, "Test Issue")
				}
				if len(request.Fields.Labels) != 2 {
					t.Errorf("Labels length = %v, want %v", len(request.Fields.Labels), 2)
				}
			},
		},
		{
			name: "minimal create issue request",
			request: CreateIssueRequest{
				Fields: IssueFields{
					Project: ProjectField{
						ID: "10001",
					},
					IssueType: IssueTypeField{
						ID: "10004",
					},
					Summary: "Minimal Issue",
				},
			},
			verify: func(t *testing.T, request CreateIssueRequest) {
				if request.Fields.Summary != "Minimal Issue" {
					t.Errorf("Summary = %v, want %v", request.Fields.Summary, "Minimal Issue")
				}
				if len(request.Fields.Labels) != 0 {
					t.Errorf("Labels length = %v, want %v", len(request.Fields.Labels), 0)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.verify != nil {
				tt.verify(t, tt.request)
			}
		})
	}
}

func TestCreateIssueResponse(t *testing.T) {
	tests := []struct {
		name     string
		response CreateIssueResponse
	}{
		{
			name: "create issue response with all fields",
			response: CreateIssueResponse{
				ID:   "10001",
				Key:  "TEST-123",
				Self: "https://example.atlassian.net/rest/api/3/issue/10001",
			},
		},
		{
			name: "empty create issue response",
			response: CreateIssueResponse{
				ID:   "",
				Key:  "",
				Self: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the struct can be created and fields accessed
			_ = tt.response.ID
			_ = tt.response.Key
			_ = tt.response.Self
		})
	}
}

// Mock server setup for testing HTTP methods
func setupMockJiraServer(t *testing.T) *httptest.Server {
	t.Helper()

	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify authentication header
		username, password, ok := r.BasicAuth()
		if !ok || username != "testuser" || password != "testtoken" {
			w.WriteHeader(http.StatusUnauthorized)
			w.Write([]byte(`{"error": "Unauthorized"}`))
			return
		}

		// Route based on path and method
		switch {
		case r.Method == "POST" && r.URL.Path == "/rest/api/3/issue":
			handleCreateIssue(w, r)
		case r.Method == "POST" && r.URL.Path == "/rest/api/3/search":
			handleFetchIssues(w, r)
		case r.Method == "GET" && strings.HasPrefix(r.URL.Path, "/rest/api/3/project/"):
			handleGetProject(w, r)
		case r.Method == "GET" && r.URL.Path == "/rest/api/3/project/NOTFOUND":
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Project not found"}`))
		default:
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(`{"error": "Not found"}`))
		}
	}))
}

func handleCreateIssue(w http.ResponseWriter, r *http.Request) {
	// Verify content type
	if r.Header.Get("Content-Type") != "application/json" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "Invalid content type"}`))
		return
	}

	// Parse request body
	var req CreateIssueRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "Invalid JSON"}`))
		return
	}

	// Validate required fields
	if req.Fields.Project.ID == "" || req.Fields.IssueType.ID == "" || req.Fields.Summary == "" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "Missing required fields"}`))
		return
	}

	// Return success response
	response := CreateIssueResponse{
		ID:   "10001",
		Key:  "TEST-123",
		Self: "https://example.atlassian.net/rest/api/3/issue/10001",
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func handleFetchIssues(w http.ResponseWriter, r *http.Request) {
	// Verify content type
	if r.Header.Get("Content-Type") != "application/json" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "Invalid content type"}`))
		return
	}

	// Parse request body
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "Invalid JSON"}`))
		return
	}

	// Return mock response
	response := JiraFetchIssueResponse{
		Expand: "names,schema",
		Issues: []JiraIssue{
			{
				ID:   "10001",
				Key:  "TEST-123",
				Self: "https://example.atlassian.net/rest/api/3/issue/10001",
				Fields: JiraFields{
					Summary: "Test Issue",
					Project: JiraProject{
						ID:   "10001",
						Key:  "TEST",
						Name: "Test Project",
					},
					Assignee: JiraUser{
						AccountID:    "123456",
						DisplayName:  "Test User",
						EmailAddress: "<EMAIL>",
					},
				},
			},
		},
		IssueErrors: []any{},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

func handleGetProject(w http.ResponseWriter, r *http.Request) {
	// Extract project key from path
	pathParts := strings.Split(r.URL.Path, "/")
	projectKey := pathParts[len(pathParts)-1]

	if projectKey == "NOTFOUND" {
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte(`{"error": "Project not found"}`))
		return
	}

	// Return mock project response
	response := Project{
		Key:         projectKey,
		Name:        "Test Project",
		ID:          "10001",
		Description: "A test project",
		IssueTypes: []IssueType{
			{
				ID:          "10004",
				Name:        "Bug",
				Description: "A bug issue type",
				Subtask:     false,
			},
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// Test CreateIssue method
func TestClient_CreateIssue(t *testing.T) {
	server := setupMockJiraServer(t)
	defer server.Close()

	client := &Client{
		DefaultClient: &client.DefaultClient{
			Client: &http.Client{Timeout: ClientTimeout},
		},
		config: ClientConfig{},
	}

	creds := Credentials{
		Username: "testuser",
		Token:    "testtoken",
	}

	description := &Description{
		Type:    "doc",
		Version: 1,
		Content: []ContentContainer{
			{
				Type: "paragraph",
				Content: []Content{
					{
						Type: "text",
						Text: "Test description",
					},
				},
			},
		},
	}

	tests := []struct {
		name        string
		creds       Credentials
		jiraURL     string
		projectID   string
		issueTypeID string
		summary     string
		description *Description
		labels      []string
		wantErr     bool
		errContains string
		verify      func(t *testing.T, response CreateIssueResponse)
	}{
		{
			name:        "successful issue creation",
			creds:       creds,
			jiraURL:     server.URL,
			projectID:   "10001",
			issueTypeID: "10004",
			summary:     "Test Issue",
			description: description,
			labels:      []string{"test", "automation"},
			wantErr:     false,
			verify: func(t *testing.T, response CreateIssueResponse) {
				if response.ID != "10001" {
					t.Errorf("CreateIssue() ID = %v, want %v", response.ID, "10001")
				}
				if response.Key != "TEST-123" {
					t.Errorf("CreateIssue() Key = %v, want %v", response.Key, "TEST-123")
				}
				if response.Self == "" {
					t.Error("CreateIssue() Self should not be empty")
				}
			},
		},
		{
			name:        "unauthorized request",
			creds:       Credentials{Username: "wrong", Token: "wrong"},
			jiraURL:     server.URL,
			projectID:   "10001",
			issueTypeID: "10004",
			summary:     "Test Issue",
			description: description,
			labels:      []string{},
			wantErr:     true,
			errContains: "failed to create JIRA issue",
		},
		{
			name:        "missing required fields",
			creds:       creds,
			jiraURL:     server.URL,
			projectID:   "",
			issueTypeID: "10004",
			summary:     "Test Issue",
			description: description,
			labels:      []string{},
			wantErr:     true,
			errContains: "failed to create JIRA issue",
		},
		{
			name:        "invalid jira URL",
			creds:       creds,
			jiraURL:     "invalid-url",
			projectID:   "10001",
			issueTypeID: "10004",
			summary:     "Test Issue",
			description: description,
			labels:      []string{},
			wantErr:     true,
			errContains: "failed to create JIRA issue",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response, err := client.CreateIssue(
				tt.creds,
				tt.jiraURL,
				tt.projectID,
				tt.issueTypeID,
				tt.summary,
				tt.description,
				tt.labels,
			)

			if tt.wantErr {
				if err == nil {
					t.Error("CreateIssue() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("CreateIssue() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("CreateIssue() unexpected error = %v", err)
				return
			}

			if tt.verify != nil {
				tt.verify(t, response)
			}
		})
	}
}

// Test FetchIssues method
func TestClientFetchIssues(t *testing.T) {
	server := setupMockJiraServer(t)
	defer server.Close()

	client := &Client{
		DefaultClient: &client.DefaultClient{
			Client: &http.Client{Timeout: ClientTimeout},
		},
		config: ClientConfig{},
	}

	creds := Credentials{
		Username: "testuser",
		Token:    "testtoken",
	}

	tests := []struct {
		name        string
		creds       Credentials
		jiraURL     string
		projectKey  string
		issueKeys   []string
		wantErr     bool
		errContains string
		verify      func(t *testing.T, response JiraFetchIssueResponse)
	}{
		{
			name:       "successful fetch issues",
			creds:      creds,
			jiraURL:    server.URL,
			projectKey: "TEST",
			issueKeys:  []string{"TEST-123"},
			wantErr:    false,
			verify: func(t *testing.T, response JiraFetchIssueResponse) {
				if len(response.Issues) != 1 {
					t.Errorf("FetchIssues() Issues length = %v, want %v", len(response.Issues), 1)
				}
				if len(response.Issues) > 0 {
					issue := response.Issues[0]
					if issue.Key != "TEST-123" {
						t.Errorf("FetchIssues() Issue.Key = %v, want %v", issue.Key, "TEST-123")
					}
					if issue.Fields.Summary != "Test Issue" {
						t.Errorf("FetchIssues() Issue.Summary = %v, want %v", issue.Fields.Summary, "Test Issue")
					}
				}
			},
		},
		{
			name:       "fetch issues without specific keys",
			creds:      creds,
			jiraURL:    server.URL,
			projectKey: "TEST",
			issueKeys:  []string{},
			wantErr:    false,
			verify: func(t *testing.T, response JiraFetchIssueResponse) {
				if len(response.Issues) != 1 {
					t.Errorf("FetchIssues() Issues length = %v, want %v", len(response.Issues), 1)
				}
			},
		},
		{
			name:        "unauthorized request",
			creds:       Credentials{Username: "wrong", Token: "wrong"},
			jiraURL:     server.URL,
			projectKey:  "TEST",
			issueKeys:   []string{},
			wantErr:     true,
			errContains: "error making request",
		},
		{
			name:        "invalid jira URL",
			creds:       creds,
			jiraURL:     "invalid-url",
			projectKey:  "TEST",
			issueKeys:   []string{},
			wantErr:     true,
			errContains: "error making request",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response, err := client.FetchIssues(
				tt.creds,
				tt.jiraURL,
				tt.projectKey,
				tt.issueKeys,
			)

			if tt.wantErr {
				if err == nil {
					t.Error("FetchIssues() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("FetchIssues() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("FetchIssues() unexpected error = %v", err)
				return
			}

			if tt.verify != nil {
				tt.verify(t, response)
			}
		})
	}
}

// Test GetProject method
func TestClientGetProject(t *testing.T) {
	server := setupMockJiraServer(t)
	defer server.Close()

	client := &Client{
		DefaultClient: &client.DefaultClient{
			Client: &http.Client{Timeout: ClientTimeout},
		},
		config: ClientConfig{},
	}

	creds := Credentials{
		Username: "testuser",
		Token:    "testtoken",
	}

	tests := []struct {
		name           string
		creds          Credentials
		jiraURL        string
		projectIDOrKey string
		wantErr        bool
		errContains    string
		verify         func(t *testing.T, project Project)
	}{
		{
			name:           "successful get project",
			creds:          creds,
			jiraURL:        server.URL,
			projectIDOrKey: "TEST",
			wantErr:        false,
			verify: func(t *testing.T, project Project) {
				if project.Key != "TEST" {
					t.Errorf("GetProject() Key = %v, want %v", project.Key, "TEST")
				}
				if project.Name != "Test Project" {
					t.Errorf("GetProject() Name = %v, want %v", project.Name, "Test Project")
				}
				if project.ID != "10001" {
					t.Errorf("GetProject() ID = %v, want %v", project.ID, "10001")
				}
				if len(project.IssueTypes) != 1 {
					t.Errorf("GetProject() IssueTypes length = %v, want %v", len(project.IssueTypes), 1)
				}
				if len(project.IssueTypes) > 0 {
					issueType := project.IssueTypes[0]
					if issueType.Name != "Bug" {
						t.Errorf("GetProject() IssueType.Name = %v, want %v", issueType.Name, "Bug")
					}
				}
			},
		},
		{
			name:           "project not found",
			creds:          creds,
			jiraURL:        server.URL,
			projectIDOrKey: "NOTFOUND",
			wantErr:        true,
			errContains:    "failed to get project",
		},
		{
			name:           "unauthorized request",
			creds:          Credentials{Username: "wrong", Token: "wrong"},
			jiraURL:        server.URL,
			projectIDOrKey: "TEST",
			wantErr:        true,
			errContains:    "failed to get project",
		},
		{
			name:           "invalid jira URL",
			creds:          creds,
			jiraURL:        "invalid-url",
			projectIDOrKey: "TEST",
			wantErr:        true,
			errContains:    "failed to get project",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			project, err := client.GetProject(
				tt.creds,
				tt.jiraURL,
				tt.projectIDOrKey,
			)

			if tt.wantErr {
				if err == nil {
					t.Error("GetProject() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("GetProject() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("GetProject() unexpected error = %v", err)
				return
			}

			if tt.verify != nil {
				tt.verify(t, project)
			}
		})
	}
}

// Test data structure tests for fetch issues
func TestJiraFetchIssueResponse(t *testing.T) {
	tests := []struct {
		name     string
		response JiraFetchIssueResponse
	}{
		{
			name: "fetch issue response with issues",
			response: JiraFetchIssueResponse{
				Expand: "names,schema",
				Issues: []JiraIssue{
					{
						ID:   "10001",
						Key:  "TEST-123",
						Self: "https://example.atlassian.net/rest/api/3/issue/10001",
						Fields: JiraFields{
							Summary: "Test Issue",
							Project: JiraProject{
								ID:   "10001",
								Key:  "TEST",
								Name: "Test Project",
							},
							Assignee: JiraUser{
								DisplayName:  "Test User",
								EmailAddress: "<EMAIL>",
							},
						},
					},
				},
				IssueErrors: []any{},
			},
		},
		{
			name: "empty fetch issue response",
			response: JiraFetchIssueResponse{
				Expand:      "",
				Issues:      []JiraIssue{},
				IssueErrors: []any{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the struct can be created and fields accessed
			_ = tt.response.Expand
			_ = tt.response.Issues
			_ = tt.response.IssueErrors

			for _, issue := range tt.response.Issues {
				if issue.ID == "" {
					t.Error("Issue ID should not be empty")
				}
				if issue.Key == "" {
					t.Error("Issue Key should not be empty")
				}
			}
		})
	}
}

// Test Project data structure
func TestProject(t *testing.T) {
	tests := []struct {
		name    string
		project Project
	}{
		{
			name: "project with all fields",
			project: Project{
				Key:         "TEST",
				Name:        "Test Project",
				ID:          "10001",
				Description: "A test project",
				IssueTypes: []IssueType{
					{
						ID:          "10004",
						Name:        "Bug",
						Description: "A bug issue type",
						Subtask:     false,
					},
				},
			},
		},
		{
			name: "minimal project",
			project: Project{
				Key:  "MIN",
				Name: "Minimal Project",
				ID:   "10002",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that the struct can be created and fields accessed
			if tt.project.Key == "" {
				t.Error("Project Key should not be empty")
			}
			if tt.project.Name == "" {
				t.Error("Project Name should not be empty")
			}
			if tt.project.ID == "" {
				t.Error("Project ID should not be empty")
			}
		})
	}
}
