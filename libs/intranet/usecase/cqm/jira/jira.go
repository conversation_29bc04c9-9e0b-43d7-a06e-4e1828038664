// Package jira provides a comprehensive Jira REST API client for the SA Intranet application.
// It implements functionality for creating issues, fetching project information, and managing
// technical debt tracking through Jira integration.
package jira

import (
	"fmt"
	"net/http"
	"time"

	"sa-intranet/http/client"

	"github.com/samber/do"
)

// ClientTimeout defines the default timeout for Jira API requests.
// Set to 30 seconds to handle potentially slow Jira responses.
var ClientTimeout = 30 * time.Second

// ErrJiraAPI is the base error for Jira API-related failures.
var ErrJiraAPI = fmt.Errorf("jira API error")

// ClientConfig holds configuration settings for the Jira client.
// Currently empty but allows for future configuration expansion.
type ClientConfig struct{}

// Credentials represents Jira authentication credentials.
// Uses username and API token for secure authentication.
type Credentials struct {
	// Username is the Jira username or email address
	Username string
	// Token is the Jira API token for authentication
	Token string
}

// Client represents a Jira API client with HTTP capabilities.
// It wraps the default HTTP client with Jira-specific functionality.
type Client struct {
	// DefaultClient provides HTTP request capabilities
	DefaultClient *client.DefaultClient
	// config holds client configuration settings
	config ClientConfig
}

// New creates a new Jira client instance with dependency injection.
// It initializes the HTTP client with appropriate timeout settings
// and configures it for Jira API communication.
//
// Parameters:
//   - i: Dependency injection container containing client configuration
//
// Returns a configured Jira client or an error if initialization fails.
func New(i *do.Injector) (*Client, error) {
	config, err := do.Invoke[ClientConfig](i)
	if err != nil {
		return nil, err
	}

	client := &Client{
		config: config,
		DefaultClient: &client.DefaultClient{
			Client: &http.Client{Timeout: ClientTimeout},
		},
	}

	return client, nil
}
