package jira

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// Content represents a text content item in Jira's Atlassian Document Format (ADF).
// It contains the actual text content and its type for rich text formatting.
type Content struct {
	// Text is the actual text content
	Text string `json:"text,omitempty"`
	// Type specifies the content type (e.g., "text", "hardBreak")
	Type string `json:"type,omitempty"`
}

// ContentContainer represents a container for content items in Jira's ADF structure.
// It groups related content items together with a specific container type.
type ContentContainer struct {
	// Type specifies the container type (e.g., "paragraph", "codeBlock")
	Type string `json:"type,omitempty"`
	// Content contains the list of content items within this container
	Content []Content `json:"content,omitempty"`
}

// Description represents a Jira issue description using Atlassian Document Format.
// It supports rich text formatting and structured content for issue descriptions.
//
//go:fieldalignment packed
type Description struct {
	// Content contains the structured content containers
	Content []ContentContainer `json:"content,omitempty"`
	// Type specifies the document type (typically "doc")
	Type string `json:"type,omitempty"`
	// Version specifies the ADF version (typically 1)
	Version int `json:"version,omitempty"`
}

// ProjectField represents a Jira project reference for issue creation.
type ProjectField struct {
	// ID is the unique project identifier
	ID string `json:"id,omitempty"`
}

// IssueTypeField represents a Jira issue type reference for issue creation.
type IssueTypeField struct {
	// ID is the unique issue type identifier
	ID string `json:"id,omitempty"`
}

// IssueFields represents all the fields required for creating a Jira issue.
// It contains the essential information needed for issue creation.
type IssueFields struct {
	// Project specifies which Jira project the issue belongs to
	Project ProjectField `json:"project"`
	// IssueType specifies the type of issue (e.g., Bug, Task, Story)
	IssueType IssueTypeField `json:"issuetype"`
	// Summary is the issue title/summary
	Summary string `json:"summary"`
	// Description contains the detailed issue description in ADF format
	Description Description `json:"description,omitempty"`
	// Labels are tags associated with the issue for categorization
	Labels []string `json:"labels,omitempty"`
}

// CreateIssueRequest represents the complete request body for creating a Jira issue.
type CreateIssueRequest struct {
	// Fields contains all the issue field data
	Fields IssueFields `json:"fields"`
}

// CreateIssueResponse represents the response from Jira after successful issue creation.
// It contains the essential identifiers for the newly created issue.
type CreateIssueResponse struct {
	// ID is the unique numeric identifier of the created issue
	ID string `json:"id"`
	// Key is the human-readable issue key (e.g., "PROJ-123")
	Key string `json:"key"`
	// Self is the REST API URL for the created issue
	Self string `json:"self"`
}

func (c *Client) CreateIssue(
	creds Credentials,
	jiraSubdomain,
	projectID,
	issueTypeID,
	summary string,
	description *Description,
	labels []string,
) (CreateIssueResponse, error) {
	var response CreateIssueResponse

	createRequest := CreateIssueRequest{
		Fields: IssueFields{
			Project: ProjectField{
				ID: projectID,
			},
			IssueType: IssueTypeField{
				ID: issueTypeID,
			},
			Summary:     summary,
			Description: *description,
			Labels:      labels,
		},
	}

	requestBody, err := json.Marshal(createRequest)
	if err != nil {
		return response, fmt.Errorf("error marshaling request: %w", err)
	}

	path := "/rest/api/3/issue"

	err = c.DefaultClient.RequestJSON(
		jiraSubdomain,
		"POST",
		path,
		bytes.NewBuffer(requestBody),
		&response,
		func(req *http.Request) {
			req.SetBasicAuth(creds.Username, creds.Token)
			req.Header.Set("Accept", "application/json")
			req.Header.Set("Content-Type", "application/json")
		},
	)
	if err != nil {
		return response, fmt.Errorf("failed to create JIRA issue: %w", err)
	}

	return response, nil
}
