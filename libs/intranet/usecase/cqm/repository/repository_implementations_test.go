package repository

import (
	"context"
	"errors"
	"testing"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// Test constructor functions with invalid injector
func TestRepositoryConstructors_ErrorHandling(t *testing.T) {
	emptyInjector := do.New()

	tests := []struct {
		name        string
		constructor func(*do.Injector) (any, error)
		wantErr     bool
	}{
		{
			name: "NewJiraProjectDefaultRepository_error_when_db_not_available",
			constructor: func(i *do.Injector) (any, error) {
				return NewJiraProjectDefaultRepository(i)
			},
			wantErr: true,
		},
		{
			name: "NewSonarqubeProjectDefaultRepository_error_when_db_not_available",
			constructor: func(i *do.Injector) (any, error) {
				return NewSonarqubeProjectDefaultRepository(i)
			},
			wantErr: true,
		},
		{
			name: "NewSonarqubeIssueDefaultRepository_error_when_db_not_available",
			constructor: func(i *do.Injector) (any, error) {
				return NewSonarqubeIssueDefaultRepository(i)
			},
			wantErr: true,
		},
		{
			name: "NewSonarqubeAnalysisDefaultRepository_error_when_db_not_available",
			constructor: func(i *do.Injector) (any, error) {
				return NewSonarqubeAnalysisDefaultRepository(i)
			},
			wantErr: true,
		},
		{
			name: "NewCompanyClientDefaultRepository_error_when_db_not_available",
			constructor: func(i *do.Injector) (any, error) {
				return NewCompanyClientDefaultRepository(i)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.constructor(emptyInjector)
			if (err != nil) != tt.wantErr {
				t.Errorf("Constructor() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Test error constants
func TestErrorConstants(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{
			name:     "ErrRecordNotFound",
			err:      ErrRecordNotFound,
			expected: "record not found",
		},
		{
			name:     "ErrProjectNotFound",
			err:      ErrProjectNotFound,
			expected: "jira project not found",
		},
		{
			name:     "ErrInvalidPage",
			err:      ErrInvalidPage,
			expected: "invalid page parameters",
		},
		{
			name:     "ErrAnalysisNil",
			err:      ErrAnalysisNil,
			expected: "analysis cannot be nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.err.Error() != tt.expected {
				t.Errorf("Error message = %v, expected %v", tt.err.Error(), tt.expected)
			}
		})
	}
}

// Test SonarAnalysisStatus validation
func TestSonarAnalysisStatus_IsValid(t *testing.T) {
	tests := []struct {
		name   string
		status model.SonarAnalysisStatus
		want   bool
	}{
		{
			name:   "valid_processing_status",
			status: model.SonarAnalysisProcessing,
			want:   true,
		},
		{
			name:   "valid_succeeded_status",
			status: model.SonarAnalysisSucceeded,
			want:   true,
		},
		{
			name:   "valid_failed_status",
			status: model.SonarAnalysisFailed,
			want:   true,
		},
		{
			name:   "invalid_empty_status",
			status: model.SonarAnalysisStatus(""),
			want:   false,
		},
		{
			name:   "invalid_unknown_status",
			status: model.SonarAnalysisStatus("unknown"),
			want:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.status.IsValid()
			if got != tt.want {
				t.Errorf("SonarAnalysisStatus.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Test filter structures
func TestFilterStructures(t *testing.T) {
	t.Run("JiraProjectFilter", func(t *testing.T) {
		filter := JiraProjectFilter{
			Name: "test project",
		}
		if filter.Name != "test project" {
			t.Errorf("JiraProjectFilter.Name = %v, want %v", filter.Name, "test project")
		}
	})

	t.Run("SonarqubeProjectFilter", func(t *testing.T) {
		filter := SonarqubeProjectFilter{
			Name:       "test project",
			ProjectKey: "test.key",
		}
		if filter.Name != "test project" {
			t.Errorf("SonarqubeProjectFilter.Name = %v, want %v", filter.Name, "test project")
		}
		if filter.ProjectKey != "test.key" {
			t.Errorf("SonarqubeProjectFilter.ProjectKey = %v, want %v", filter.ProjectKey, "test.key")
		}
	})

	t.Run("SonarqubeIssueFilter", func(t *testing.T) {
		filter := SonarqubeIssueFilter{
			IssueIDs: []string{"issue1", "issue2"},
		}
		if len(filter.IssueIDs) != 2 {
			t.Errorf("SonarqubeIssueFilter.IssueIDs length = %v, want %v", len(filter.IssueIDs), 2)
		}
		if filter.IssueIDs[0] != "issue1" {
			t.Errorf("SonarqubeIssueFilter.IssueIDs[0] = %v, want %v", filter.IssueIDs[0], "issue1")
		}
	})

	t.Run("SonarqubeAnalysisFilter", func(t *testing.T) {
		filter := SonarqubeAnalysisFilter{
			AnalysisID: "analysis123",
		}
		if filter.AnalysisID != "analysis123" {
			t.Errorf("SonarqubeAnalysisFilter.AnalysisID = %v, want %v", filter.AnalysisID, "analysis123")
		}
	})

	t.Run("CompanyClientFilter", func(t *testing.T) {
		filter := CompanyClientFilter{
			Name: "test client",
		}
		if filter.Name != "test client" {
			t.Errorf("CompanyClientFilter.Name = %v, want %v", filter.Name, "test client")
		}
	})
}

// Test interface compliance at compile time
func TestInterfaceCompliance(t *testing.T) {
	t.Run("repository_interfaces_are_implemented", func(t *testing.T) {
		// These will fail to compile if interfaces are not implemented correctly
		var _ JiraProjectRepository = (*JiraProjectDefaultRepository)(nil)
		var _ SonarqubeProjectRepository = (*SonarqubeProjectDefaultRepository)(nil)
		var _ SonarqubeIssueRepository = (*SonarqubeIssueDefaultRepository)(nil)
		var _ SonarqubeAnalysisRepository = (*SonarqubeAnalysisDefaultRepository)(nil)
		var _ CompanyClientRepository = (*CompanyClientDefaultRepository)(nil)
	})
}

// Test SonarqubeAnalysisDefaultRepository specific logic
func TestSonarqubeAnalysisDefaultRepository_SaveValidation(t *testing.T) {
	// Create a mock repository to test the validation logic
	repo := &SonarqubeAnalysisDefaultRepository{}

	tests := []struct {
		name     string
		analysis *model.SonarqubeAnalysis
		forceID  bool
		wantErr  bool
		errType  error
	}{
		{
			name:     "error_save_nil_analysis",
			analysis: nil,
			forceID:  false,
			wantErr:  true,
			errType:  ErrAnalysisNil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := repo.Save(context.Background(), tt.analysis, tt.forceID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.errType != nil {
				if !errors.Is(err, tt.errType) {
					t.Errorf("Save() error = %v, expected error type %v", err, tt.errType)
				}
			}
		})
	}
}

// Test pagination validation in SonarqubeAnalysisDefaultRepository
func TestSonarqubeAnalysisDefaultRepository_ListValidation(t *testing.T) {
	repo := &SonarqubeAnalysisDefaultRepository{}

	tests := []struct {
		name    string
		params  repository.PaginationParams[SonarqubeAnalysisFilter]
		wantErr bool
		errType error
	}{
		{
			name: "error_invalid_page_zero",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     0,
				PageSize: 10,
			},
			wantErr: true,
			errType: ErrInvalidPage,
		},
		{
			name: "error_invalid_page_negative",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     -1,
				PageSize: 10,
			},
			wantErr: true,
			errType: ErrInvalidPage,
		},
		{
			name: "error_invalid_page_size_zero",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     1,
				PageSize: 0,
			},
			wantErr: true,
			errType: ErrInvalidPage,
		},
		{
			name: "error_invalid_page_size_negative",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     1,
				PageSize: -1,
			},
			wantErr: true,
			errType: ErrInvalidPage,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := repo.List(context.Background(), tt.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.errType != nil {
				if !errors.Is(err, tt.errType) {
					t.Errorf("List() error = %v, expected error type %v", err, tt.errType)
				}
			}
		})
	}
}

// Test SonarqubeProjectDefaultRepository specific methods
func TestSonarqubeProjectDefaultRepository_Methods(t *testing.T) {
	t.Run("repository_struct_creation", func(t *testing.T) {
		repo := &SonarqubeProjectDefaultRepository{}
		if repo == nil {
			t.Error("Expected repository to be created, got nil")
		}
	})

	t.Run("filter_struct_validation", func(t *testing.T) {
		filter := SonarqubeProjectFilter{
			Name:       "test project",
			ProjectKey: "test.key",
		}
		if filter.Name != "test project" {
			t.Errorf("Expected Name 'test project', got %s", filter.Name)
		}
		if filter.ProjectKey != "test.key" {
			t.Errorf("Expected ProjectKey 'test.key', got %s", filter.ProjectKey)
		}
	})
}

// Test SonarqubeIssueDefaultRepository specific methods
func TestSonarqubeIssueDefaultRepository_Methods(t *testing.T) {
	t.Run("repository_struct_creation", func(t *testing.T) {
		repo := &SonarqubeIssueDefaultRepository{}
		if repo == nil {
			t.Error("Expected repository to be created, got nil")
		}
	})

	t.Run("filter_struct_validation", func(t *testing.T) {
		filter := SonarqubeIssueFilter{
			IssueIDs: []string{"issue1", "issue2", "issue3"},
		}
		if len(filter.IssueIDs) != 3 {
			t.Errorf("Expected 3 issue IDs, got %d", len(filter.IssueIDs))
		}
		if filter.IssueIDs[0] != "issue1" {
			t.Errorf("Expected first issue ID 'issue1', got %s", filter.IssueIDs[0])
		}
	})
}

// Test JiraProjectDefaultRepository specific methods
func TestJiraProjectDefaultRepository_Methods(t *testing.T) {
	t.Run("repository_struct_creation", func(t *testing.T) {
		repo := &JiraProjectDefaultRepository{}
		if repo == nil {
			t.Error("Expected repository to be created, got nil")
		}
	})

	t.Run("model_validation", func(t *testing.T) {
		project := &model.JiraProject{
			Name:       "Test Project",
			ProjectKey: "TEST",
			JiraURL:    "https://test.atlassian.net",
			Username:   "testuser",
			Token:      "testtoken",
			ProjectID:  "10001",
			Active:     true,
		}
		if project.Name != "Test Project" {
			t.Errorf("Expected Name 'Test Project', got %s", project.Name)
		}
		if project.ProjectKey != "TEST" {
			t.Errorf("Expected ProjectKey 'TEST', got %s", project.ProjectKey)
		}
		if !project.Active {
			t.Error("Expected Active to be true")
		}
	})
}

// Test CompanyClientDefaultRepository specific methods
func TestCompanyClientDefaultRepository_Methods(t *testing.T) {
	t.Run("repository_struct_creation", func(t *testing.T) {
		repo := &CompanyClientDefaultRepository{}
		if repo == nil {
			t.Error("Expected repository to be created, got nil")
		}
	})

	t.Run("model_validation", func(t *testing.T) {
		client := &model.CompanyClient{
			Name: "Test Client",
		}
		if client.Name != "Test Client" {
			t.Errorf("Expected Name 'Test Client', got %s", client.Name)
		}
	})
}

// Test SonarqubeAnalysisDefaultRepository additional methods
func TestSonarqubeAnalysisDefaultRepository_AdditionalMethods(t *testing.T) {
	t.Run("repository_struct_creation", func(t *testing.T) {
		repo := &SonarqubeAnalysisDefaultRepository{}
		if repo == nil {
			t.Error("Expected repository to be created, got nil")
		}
	})

	t.Run("model_validation", func(t *testing.T) {
		analysis := &model.SonarqubeAnalysis{
			SonarqubeProjectID: uuid.New(),
			AnalysisID:         "test-analysis-123",
			Status:             model.SonarAnalysisSucceeded,
		}
		if analysis.AnalysisID != "test-analysis-123" {
			t.Errorf("Expected AnalysisID 'test-analysis-123', got %s", analysis.AnalysisID)
		}
		if analysis.Status != model.SonarAnalysisSucceeded {
			t.Errorf("Expected Status %s, got %s", model.SonarAnalysisSucceeded, analysis.Status)
		}
		if analysis.SonarqubeProjectID == uuid.Nil {
			t.Error("Expected non-nil SonarqubeProjectID")
		}
	})

	t.Run("pagination_params_validation", func(t *testing.T) {
		params := repository.PaginationParams[SonarqubeAnalysisFilter]{
			Page:     1,
			PageSize: 10,
			Filters: SonarqubeAnalysisFilter{
				AnalysisID: "test-analysis",
			},
		}
		if params.Page != 1 {
			t.Errorf("Expected Page 1, got %d", params.Page)
		}
		if params.PageSize != 10 {
			t.Errorf("Expected PageSize 10, got %d", params.PageSize)
		}
		if params.Filters.AnalysisID != "test-analysis" {
			t.Errorf("Expected AnalysisID 'test-analysis', got %s", params.Filters.AnalysisID)
		}
	})
}

// Test model validation and business logic
func TestModelValidation(t *testing.T) {
	t.Run("JiraProject_validation", func(t *testing.T) {
		project := &model.JiraProject{
			ID:         uuid.New(),
			JiraURL:    "https://test.atlassian.net",
			Username:   "testuser",
			Token:      "testtoken",
			Name:       "Test Project",
			ProjectID:  "10001",
			ProjectKey: "TEST",
			Active:     true,
		}

		// Test all fields are set correctly
		if project.ID == uuid.Nil {
			t.Error("Expected non-nil ID")
		}
		if project.JiraURL != "https://test.atlassian.net" {
			t.Errorf("Expected JiraURL 'https://test.atlassian.net', got %s", project.JiraURL)
		}
		if project.Username != "testuser" {
			t.Errorf("Expected Username 'testuser', got %s", project.Username)
		}
		if project.Token != "testtoken" {
			t.Errorf("Expected Token 'testtoken', got %s", project.Token)
		}
		if project.Name != "Test Project" {
			t.Errorf("Expected Name 'Test Project', got %s", project.Name)
		}
		if project.ProjectID != "10001" {
			t.Errorf("Expected ProjectID '10001', got %s", project.ProjectID)
		}
		if project.ProjectKey != "TEST" {
			t.Errorf("Expected ProjectKey 'TEST', got %s", project.ProjectKey)
		}
		if !project.Active {
			t.Error("Expected Active to be true")
		}
	})

	t.Run("SonarqubeProject_validation", func(t *testing.T) {
		project := &model.SonarqubeProject{
			ID:            uuid.New(),
			ProjectName:   "Test Sonar Project",
			ProjectKey:    "test.sonar.project",
			Branch:        "main",
			Active:        true,
			JiraProjectID: uuid.New(),
		}

		// Test all fields are set correctly
		if project.ID == uuid.Nil {
			t.Error("Expected non-nil ID")
		}
		if project.ProjectName != "Test Sonar Project" {
			t.Errorf("Expected ProjectName 'Test Sonar Project', got %s", project.ProjectName)
		}
		if project.ProjectKey != "test.sonar.project" {
			t.Errorf("Expected ProjectKey 'test.sonar.project', got %s", project.ProjectKey)
		}
		if project.Branch != "main" {
			t.Errorf("Expected Branch 'main', got %s", project.Branch)
		}
		if !project.Active {
			t.Error("Expected Active to be true")
		}
		if project.JiraProjectID == uuid.Nil {
			t.Error("Expected non-nil JiraProjectID")
		}
	})

	t.Run("SonarqubeIssue_validation", func(t *testing.T) {
		issue := &model.SonarqubeIssue{
			ID:                 uuid.New(),
			IssueID:            "test-issue-123",
			SonarqubeProjectID: uuid.New(),
			JiraIssueID:        "JIRA-123",
			JiraIssueKey:       "TEST-123",
		}

		// Test all fields are set correctly
		if issue.ID == uuid.Nil {
			t.Error("Expected non-nil ID")
		}
		if issue.IssueID != "test-issue-123" {
			t.Errorf("Expected IssueID 'test-issue-123', got %s", issue.IssueID)
		}
		if issue.SonarqubeProjectID == uuid.Nil {
			t.Error("Expected non-nil SonarqubeProjectID")
		}
		if issue.JiraIssueID != "JIRA-123" {
			t.Errorf("Expected JiraIssueID 'JIRA-123', got %s", issue.JiraIssueID)
		}
		if issue.JiraIssueKey != "TEST-123" {
			t.Errorf("Expected JiraIssueKey 'TEST-123', got %s", issue.JiraIssueKey)
		}
	})

	t.Run("SonarqubeAnalysis_validation", func(t *testing.T) {
		analysis := &model.SonarqubeAnalysis{
			ID:                 uuid.New(),
			SonarqubeProjectID: uuid.New(),
			AnalysisID:         "test-analysis-456",
			WebhookPayload:     map[string]interface{}{"test": "payload"},
			Measures:           map[string]interface{}{"coverage": "80.5"},
			Score:              80.5,
			Status:             model.SonarAnalysisSucceeded,
			QualityGateName:    "Sonar way",
			QualityGateStatus:  "OK",
			ErrorDetails:       map[string]interface{}{},
			IssuesTotal:        5,
			IssuesEffortTotal:  25,
		}

		// Test all fields are set correctly
		if analysis.ID == uuid.Nil {
			t.Error("Expected non-nil ID")
		}
		if analysis.SonarqubeProjectID == uuid.Nil {
			t.Error("Expected non-nil SonarqubeProjectID")
		}
		if analysis.AnalysisID != "test-analysis-456" {
			t.Errorf("Expected AnalysisID 'test-analysis-456', got %s", analysis.AnalysisID)
		}
		if analysis.Score != 80.5 {
			t.Errorf("Expected Score 80.5, got %f", analysis.Score)
		}
		if analysis.Status != model.SonarAnalysisSucceeded {
			t.Errorf("Expected Status %s, got %s", model.SonarAnalysisSucceeded, analysis.Status)
		}
		if analysis.QualityGateName != "Sonar way" {
			t.Errorf("Expected QualityGateName 'Sonar way', got %s", analysis.QualityGateName)
		}
		if analysis.QualityGateStatus != "OK" {
			t.Errorf("Expected QualityGateStatus 'OK', got %s", analysis.QualityGateStatus)
		}
		if analysis.IssuesTotal != 5 {
			t.Errorf("Expected IssuesTotal 5, got %d", analysis.IssuesTotal)
		}
		if analysis.IssuesEffortTotal != 25 {
			t.Errorf("Expected IssuesEffortTotal 25, got %d", analysis.IssuesEffortTotal)
		}
	})

	t.Run("CompanyClient_validation", func(t *testing.T) {
		client := &model.CompanyClient{
			ID:   uuid.New(),
			Name: "Test Company Client",
		}

		// Test all fields are set correctly
		if client.ID == uuid.Nil {
			t.Error("Expected non-nil ID")
		}
		if client.Name != "Test Company Client" {
			t.Errorf("Expected Name 'Test Company Client', got %s", client.Name)
		}
	})
}
