package repository

import (
	"context"
	"errors"
	"fmt"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

var ErrAnalysisNil = errors.New("analysis cannot be nil")

// SonarqubeAnalysisDefaultRepository is the default implementation of SonarqubeAnalysisRepository
type SonarqubeAnalysisDefaultRepository struct {
	*bun.DB
}

// compile time check if SonarqubeAnalysisDefaultRepository implements SonarqubeAnalysisRepository
var _ SonarqubeAnalysisRepository = (*SonarqubeAnalysisDefaultRepository)(nil)

type SonarqubeAnalysisFilter struct {
	AnalysisID string
}

// NewSonarqubeAnalysisDefaultRepository creates a new instance of SonarqubeAnalysisDefaultRepository
func NewSonarqubeAnalysisDefaultRepository(i *do.Injector) (SonarqubeAnalysisRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &SonarqubeAnalysisDefaultRepository{
		DB: db,
	}, nil
}

// List returns a paginated list of SonarqubeAnalyses
func (r *SonarqubeAnalysisDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[SonarqubeAnalysisFilter],
) (*repository.PaginatedResult[model.SonarqubeAnalysis], error) {
	// Validate pagination parameters
	if params.Page < 1 || params.PageSize < 1 {
		return nil, ErrInvalidPage
	}

	query := r.DB.NewSelect().
		Model((*model.SonarqubeAnalysis)(nil))

	if params.Filters.AnalysisID != "" {
		query = query.Where("analysis_id = ?", params.Filters.AnalysisID)
	}

	// Calculate offset for pagination
	offset := (params.Page - 1) * params.PageSize

	// Query to get the items for the current page
	analyses := []model.SonarqubeAnalysis{}

	paginationQuery := query.Clone().
		Limit(params.PageSize).
		Offset(offset)

	// Execute the paginationQuery
	if err := paginationQuery.Scan(ctx, &analyses); err != nil {
		return nil, fmt.Errorf("failed to list sonarqube analyses in paginationQuery: %w", err)
	}

	// Count total items for pagination metadata
	var totalItems int64
	countQuery := query.Clone()

	countResult, err := countQuery.Count(ctx)
	if err != nil {
		return nil, err
	}

	totalItems = int64(countResult)

	// Calculate total pages
	totalPages := int(totalItems) / params.PageSize
	if int(totalItems)%params.PageSize > 0 {
		totalPages++
	}

	return &repository.PaginatedResult[model.SonarqubeAnalysis]{
		Items:      analyses,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       params.Page,
		PageSize:   params.PageSize,
	}, nil
}

// Save persists a SonarqubeAnalysis to the database
func (r *SonarqubeAnalysisDefaultRepository) Save(ctx context.Context, analysis *model.SonarqubeAnalysis, forceID bool) error {
	// Basic validation
	if analysis == nil {
		return ErrAnalysisNil
	}

	if analysis.ID == uuid.Nil || forceID {
		// Insert new analysis
		_, err := r.DB.NewInsert().
			Model(analysis).
			Returning("*"). // Ensure the inserted values are updated in the struct
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to insert sonarqube analysis: %w", err)
		}
	} else {
		// Update existing analysis
		res, err := r.DB.NewUpdate().
			Model(analysis).
			WherePK().
			Returning("*").
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to update sonarqube analysis: %w", err)
		}

		rowsAffected, err := res.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrRecordNotFound
		}
	}

	return nil
}
