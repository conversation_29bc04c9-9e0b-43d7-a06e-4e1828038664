package repository

import (
	"context"
	"errors"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
)

var (
	ErrRecordNotFound  = errors.New("record not found")
	ErrProjectNotFound = errors.New("jira project not found")

	ErrInvalidPage = errors.New("invalid page parameters")
)

type JiraProjectRepository interface {
	Find(ctx context.Context, id uuid.UUID) (*model.JiraProject, error)
	List(ctx context.Context, params repository.PaginationParams[JiraProjectFilter]) (*repository.PaginatedResult[model.JiraProject], error)
	Save(ctx context.Context, project *model.JiraProject) (*model.JiraProject, error)
}

// SonarqubeProjectRepository defines the interface for SonarqubeProject data access
type SonarqubeProjectRepository interface {
	Find(ctx context.Context, id uuid.UUID) (*model.SonarqubeProject, error)
	List(ctx context.Context, params repository.PaginationParams[SonarqubeProjectFilter]) (*repository.PaginatedResult[model.SonarqubeProject], error)
	Save(ctx context.Context, project *model.SonarqubeProject, forceID bool) (*model.SonarqubeProject, error)
}

// SonarqubeIssueRepository defines the interface for SonarqubeIssue data access
type SonarqubeIssueRepository interface {
	// FindByIssueID retrieves a SonarqubeIssue by its IssueID
	FindByIssueID(ctx context.Context, issueID string) (*model.SonarqubeIssue, error)
	Find(ctx context.Context, id uuid.UUID) (*model.SonarqubeIssue, error)
	List(ctx context.Context, params repository.PaginationParams[SonarqubeIssueFilter]) (*repository.PaginatedResult[model.SonarqubeIssue], error)
	Save(ctx context.Context, issue *model.SonarqubeIssue) error
}

type SonarqubeAnalysisRepository interface {
	List(ctx context.Context, params repository.PaginationParams[SonarqubeAnalysisFilter]) (*repository.PaginatedResult[model.SonarqubeAnalysis], error)
	Save(ctx context.Context, analysis *model.SonarqubeAnalysis, forceID bool) error
}

// CompanyClientRepository defines the interface for CompanyClient data access
type CompanyClientRepository interface {
	Find(ctx context.Context, id uuid.UUID) (*model.CompanyClient, error)
	List(ctx context.Context, params repository.PaginationParams[CompanyClientFilter]) (*repository.PaginatedResult[model.CompanyClient], error)
	Save(ctx context.Context, project *model.CompanyClient) (*model.CompanyClient, error)
}

// NewPaginatedResult creates a new PaginatedResult with initialized fields
func NewPaginatedResult[T any](items []T, totalItems int64, page, pageSize int) *repository.PaginatedResult[T] {
	if items == nil {
		items = make([]T, 0) // Initialize as empty slice instead of nil
	}

	totalPages := int(totalItems) / pageSize
	if int(totalItems)%pageSize > 0 {
		totalPages++
	}

	return &repository.PaginatedResult[T]{
		Items:      items,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   pageSize,
	}
}
