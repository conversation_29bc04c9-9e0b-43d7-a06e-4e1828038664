package repository

import (
	"context"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// SonarqubeIssueDefaultRepository is the default implementation of SonarqubeIssueRepository
type SonarqubeIssueDefaultRepository struct {
	*bun.DB
}

// compile time check if SonarqubeIssueDefaultRepository implements SonarqubeIssueRepository
var _ SonarqubeIssueRepository = (*SonarqubeIssueDefaultRepository)(nil)

type SonarqubeIssueFilter struct {
	IssueIDs []string `json:"issue_ids,omitempty"`
}

// NewSonarqubeIssueDefaultRepository creates a new instance of SonarqubeIssueDefaultRepository
func NewSonarqubeIssueDefaultRepository(i *do.Injector) (SonarqubeIssueRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &SonarqubeIssueDefaultRepository{
		DB: db,
	}, nil
}

// Find retrieves a SonarqubeIssue by its ID
func (r *SonarqubeIssueDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.SonarqubeIssue, error) {
	record, err := repository.Find[model.SonarqubeIssue](
		ctx,
		r.DB,
		id,
	)

	return record, err
}

// FindByIssueID retrieves a SonarqubeIssue by its IssueID
func (r *SonarqubeIssueDefaultRepository) FindByIssueID(ctx context.Context, issueID string) (*model.SonarqubeIssue, error) {
	issue := new(model.SonarqubeIssue)

	err := r.DB.NewSelect().
		Model(issue).
		Where("issue_id = ?", issueID).
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return issue, nil
}

// List returns a paginated list of SonarqubeIssues
func (r *SonarqubeIssueDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[SonarqubeIssueFilter],
) (*repository.PaginatedResult[model.SonarqubeIssue], error) {
	return repository.FindAll[model.SonarqubeIssue](
		ctx,
		r.DB,
		params,
	)
}

// Save persists a SonarqubeIssue to the database
func (r *SonarqubeIssueDefaultRepository) Save(ctx context.Context, issue *model.SonarqubeIssue) error {
	err := repository.Save(
		ctx,
		r.DB,
		issue,
		repository.WithSaveNew(issue.ID == uuid.Nil),
	)
	if err != nil {
		return err
	}

	return nil
}
