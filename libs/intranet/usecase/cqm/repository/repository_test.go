package repository

import (
	"testing"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"
)

func TestNewPaginatedResult(t *testing.T) {
	tests := []struct {
		name       string
		items      []model.JiraProject
		totalItems int64
		page       int
		pageSize   int
		want       *repository.PaginatedResult[model.JiraProject]
		verify     func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int)
	}{
		{
			name:       "empty_items_nil_slice",
			items:      nil,
			totalItems: 0,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				if got.Items == nil {
					t.Error("expected Items to be initialized as empty slice, got nil")
				}
				if len(got.Items) != 0 {
					t.<PERSON>rrorf("expected empty Items slice, got length %d", len(got.Items))
				}
				if got.TotalItems != totalItems {
					t.Errorf("expected TotalItems %d, got %d", totalItems, got.TotalItems)
				}
				if got.TotalPages != 0 {
					t.Errorf("expected TotalPages 0, got %d", got.TotalPages)
				}
				if got.Page != page {
					t.Errorf("expected Page %d, got %d", page, got.Page)
				}
				if got.PageSize != pageSize {
					t.Errorf("expected PageSize %d, got %d", pageSize, got.PageSize)
				}
			},
		},
		{
			name: "with_items_calculates_pages_correctly",
			items: []model.JiraProject{
				{Name: "Project 1"},
				{Name: "Project 2"},
			},
			totalItems: 25,
			page:       2,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				if len(got.Items) != len(items) {
					t.Errorf("expected Items length %d, got %d", len(items), len(got.Items))
				}
				if got.TotalItems != totalItems {
					t.Errorf("expected TotalItems %d, got %d", totalItems, got.TotalItems)
				}
				expectedPages := 3 // 25 items / 10 per page = 3 pages
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
				if got.Page != page {
					t.Errorf("expected Page %d, got %d", page, got.Page)
				}
				if got.PageSize != pageSize {
					t.Errorf("expected PageSize %d, got %d", pageSize, got.PageSize)
				}
			},
		},
		{
			name: "exact_division_pages",
			items: []model.JiraProject{
				{Name: "Project 1"},
			},
			totalItems: 20,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				expectedPages := 2 // 20 items / 10 per page = 2 pages exactly
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
			},
		},
		{
			name:       "single_item_single_page",
			items:      []model.JiraProject{{Name: "Single Project"}},
			totalItems: 1,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				expectedPages := 1 // 1 item / 10 per page = 1 page
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
				if len(got.Items) != 1 {
					t.Errorf("expected 1 item, got %d", len(got.Items))
				}
				if got.Items[0].Name != "Single Project" {
					t.Errorf("expected item name 'Single Project', got %s", got.Items[0].Name)
				}
			},
		},
		{
			name:       "large_page_size",
			items:      []model.JiraProject{{Name: "Project"}},
			totalItems: 5,
			page:       1,
			pageSize:   100,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				expectedPages := 1 // 5 items / 100 per page = 1 page
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
			},
		},
		{
			name:       "zero_total_items",
			items:      []model.JiraProject{},
			totalItems: 0,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[model.JiraProject], items []model.JiraProject, totalItems int64, page, pageSize int) {
				if got.TotalPages != 0 {
					t.Errorf("expected TotalPages 0 for zero items, got %d", got.TotalPages)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewPaginatedResult(tt.items, tt.totalItems, tt.page, tt.pageSize)

			if tt.verify != nil {
				tt.verify(t, got, tt.items, tt.totalItems, tt.page, tt.pageSize)
			}
		})
	}
}

func TestNewPaginatedResultGeneric(t *testing.T) {
	// Test with different types to ensure generics work correctly
	t.Run("string_type", func(t *testing.T) {
		items := []string{"item1", "item2", "item3"}
		result := NewPaginatedResult(items, 3, 1, 10)

		if len(result.Items) != 3 {
			t.Errorf("expected 3 items, got %d", len(result.Items))
		}
		if result.Items[0] != "item1" {
			t.Errorf("expected first item 'item1', got %s", result.Items[0])
		}
	})

	t.Run("int_type", func(t *testing.T) {
		items := []int{1, 2, 3, 4, 5}
		result := NewPaginatedResult(items, 5, 1, 3)

		if len(result.Items) != 5 {
			t.Errorf("expected 5 items, got %d", len(result.Items))
		}
		if result.TotalPages != 2 {
			t.Errorf("expected 2 pages, got %d", result.TotalPages)
		}
	})

	t.Run("company_client_type", func(t *testing.T) {
		items := []model.CompanyClient{
			{Name: "Client 1"},
			{Name: "Client 2"},
		}
		result := NewPaginatedResult(items, 2, 1, 10)

		if len(result.Items) != 2 {
			t.Errorf("expected 2 items, got %d", len(result.Items))
		}
		if result.Items[0].Name != "Client 1" {
			t.Errorf("expected first client 'Client 1', got %s", result.Items[0].Name)
		}
	})
}

func TestNewPaginatedResultEdgeCases(t *testing.T) {
	tests := []struct {
		name       string
		items      []model.JiraProject
		totalItems int64
		page       int
		pageSize   int
		verify     func(t *testing.T, result *repository.PaginatedResult[model.JiraProject])
	}{
		{
			name:       "page_size_one",
			items:      []model.JiraProject{{Name: "Project"}},
			totalItems: 5,
			page:       3,
			pageSize:   1,
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				if result.TotalPages != 5 {
					t.Errorf("expected 5 pages with page size 1, got %d", result.TotalPages)
				}
				if result.Page != 3 {
					t.Errorf("expected page 3, got %d", result.Page)
				}
			},
		},
		{
			name:       "large_total_items",
			items:      []model.JiraProject{},
			totalItems: 1000000,
			page:       1,
			pageSize:   50,
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				expectedPages := 20000 // 1000000 / 50
				if result.TotalPages != expectedPages {
					t.Errorf("expected %d pages, got %d", expectedPages, result.TotalPages)
				}
			},
		},
		{
			name:       "items_mismatch_total",
			items:      []model.JiraProject{{Name: "Project"}}, // 1 item in slice
			totalItems: 100,                                    // but total is 100
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				// Should still calculate pages based on totalItems, not slice length
				if result.TotalPages != 10 {
					t.Errorf("expected 10 pages based on totalItems, got %d", result.TotalPages)
				}
				if len(result.Items) != 1 {
					t.Errorf("expected 1 item in slice, got %d", len(result.Items))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewPaginatedResult(tt.items, tt.totalItems, tt.page, tt.pageSize)
			if tt.verify != nil {
				tt.verify(t, result)
			}
		})
	}
}

func TestNewPaginatedResultNilSliceHandling(t *testing.T) {
	// Test that nil slices are properly converted to empty slices
	result := NewPaginatedResult[model.JiraProject](nil, 0, 1, 10)

	if result.Items == nil {
		t.Error("expected Items to be empty slice, got nil")
	}

	if len(result.Items) != 0 {
		t.Errorf("expected empty slice length 0, got %d", len(result.Items))
	}

	// Verify we can safely iterate over the slice
	count := 0
	for range result.Items {
		count++
	}
	if count != 0 {
		t.Errorf("expected 0 iterations over empty slice, got %d", count)
	}

	// Verify we can safely append to the slice
	result.Items = append(result.Items, model.JiraProject{Name: "Test"})
	if len(result.Items) != 1 {
		t.Errorf("expected 1 item after append, got %d", len(result.Items))
	}
}
