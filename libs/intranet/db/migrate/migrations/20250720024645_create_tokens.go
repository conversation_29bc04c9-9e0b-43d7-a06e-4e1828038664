package migrations

import (
	"context"
	"log"

	"sa-intranet/usecase/auth/model"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] Token")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.Token)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add indexes for better query performance
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_tokens_expires_at ON Tokens (expires_at)`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] Token")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.Token)(nil)).
				IfExists().
				Exec(ctx)
			if err != nil {
				return err
			}

			return nil
		})
	})
}
