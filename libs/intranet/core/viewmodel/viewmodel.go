// Package viewmodel provides standardized response structures for API endpoints.
// It defines common patterns for success/error responses and error handling.
package viewmodel

// ErrorItem represents a single error with a message and error code.
// This structure provides consistent error reporting across all API responses.
type ErrorItem struct {
	// Message contains the human-readable error description
	Message string `json:"message"`
	// ErrorCode contains the machine-readable error identifier
	ErrorCode string `json:"code"`
}

// Response is a generic wrapper for API responses that includes success status,
// data payload, and error information. It provides a consistent structure
// for all API endpoints in the application.
//
// Type Parameters:
//   - T: The type of data being returned in successful responses
type Response[T any] struct {
	// Success indicates whether the operation completed successfully
	Success bool `json:"success"`
	// Data contains the response payload for successful operations
	Data T `json:"data,omitempty"`
	// Errors contains a list of errors that occurred during processing
	Errors []ErrorItem `json:"errors,omitempty"`
}
