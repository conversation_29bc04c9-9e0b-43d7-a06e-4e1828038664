package core

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"

	"github.com/samber/do"
)

// Define static errors
var (
	ErrInvalidKeyLength   = errors.New("key length must be 16, 24, or 32 bytes")
	ErrCiphertextTooShort = errors.New("ciphertext too short")
)

// Cy<PERSON> defines the interface for encryption and decryption operations.
// Implementations should provide secure encryption using industry-standard algorithms.
type Cypher interface {
	// Encrypt converts plaintext to encrypted format and returns base64 encoded string
	Encrypt(plaintext string) (string, error)
	// Decrypt converts encrypted text back to plaintext from base64 encoded input
	Decrypt(encryptedText string) (string, error)
}

// CypherConfig holds the configuration for encryption services.
// The Key should be base64 encoded and must be 16, 24, or 32 bytes for AES-128, AES-192, or AES-256.
type CypherConfig struct {
	// Key is the base64 encoded encryption key loaded from environment variable APP_CYPHER_KEY
	Key string `mapstructure:"APP_CYPHER_KEY"`
}

// AESCipher provides encryption and decryption functionality using AES
type AESCipher struct {
	key []byte
}

// NewAESCipher creates a new AESCipher with the provided key
// The key must be 16, 24, or 32 bytes to select AES-128, AES-192, or AES-256
func NewAESCipher(i *do.Injector) (Cypher, error) {
	conf, err := do.Invoke[CypherConfig](i)
	if err != nil {
		return nil, err
	}

	confKey, err := base64.StdEncoding.DecodeString(conf.Key)
	if err != nil {
		return nil, err
	}

	key := confKey

	keyLen := len(key)

	if keyLen != 16 && keyLen != 24 && keyLen != 32 {
		return nil, ErrInvalidKeyLength
	}

	return &AESCipher{key: key}, nil
}

// Encrypt encrypts plaintext using AES in GCM mode and returns base64 encoded string
func (a *AESCipher) Encrypt(plaintext string) (string, error) {
	block, err := aes.NewCipher(a.key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt decrypts a base64 encoded ciphertext using AES in GCM mode
func (a *AESCipher) Decrypt(encryptedText string) (string, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(a.key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	if len(ciphertext) < gcm.NonceSize() {
		return "", ErrCiphertextTooShort
	}

	nonce, ciphertext := ciphertext[:gcm.NonceSize()], ciphertext[gcm.NonceSize():]

	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
