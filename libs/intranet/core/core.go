// Package core provides fundamental utilities and services for the SA Intranet application.
// It includes encryption, caching, and dependency injection registration functionality.
package core

import (
	"sa-intranet/core/cache"

	"github.com/samber/do"
)

// Error formatting constants used throughout the application for consistent error wrapping.
// These constants provide standardized templates for error message formatting.
const (
	// ErrFormatWithCause formats an error with another error as the cause
	ErrFormatWithCause = "%w: %w"
	// ErrFormatWithString formats an error with a string message
	ErrFormatWithString = "%w: %s"
	// ErrFormatWithType formats an error with a type information
	ErrFormatWithType = "%w: %T"
)

// Register initializes and registers all core services with the dependency injection container.
// This function sets up the fundamental services required by the application including
// encryption, caching, and configuration management.
//
// Parameters:
//   - i: The dependency injection container
//   - conf: Configuration for the cipher service
//
// Returns an error if any service registration fails.
func Register(i *do.Injector, conf CypherConfig) error {
	// Register cypher config for encryption services
	do.Provide(i, func(i *do.Injector) (CypherConfig, error) {
		return conf, nil
	})

	// Register cache service for application-wide caching
	do.Provide(i, cache.NewDefaultCache)

	// Register AES cipher service for encryption/decryption operations
	do.Provide(i, NewAESCipher)

	return nil
}
