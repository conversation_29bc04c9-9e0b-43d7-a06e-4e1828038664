package cache

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/samber/do"
)

var (
	// ErrKeyNotFound is returned when a key is not found in the cache
	ErrKeyNotFound = errors.New("key not found in cache")
	// ErrKeyExpired is returned when a key has expired
	ErrKeyExpired = errors.New("key has expired")
	// ErrUnexpectedValueType is returned when a value in the cache is not of the expected type
	ErrUnexpectedValueType = errors.New("unexpected value type")
)

// cacheItem represents an item stored in the cache
type cacheItem struct {
	value      []byte
	expiration time.Time
}

// DefaultCache implements the Cache interface using sync.Map
type DefaultCache struct {
	items           sync.Map
	cleanUpInterval time.Duration
}

// NewDefaultCache creates a new instance of DefaultCache
func NewDefaultCache(i *do.Injector) (Cache, error) {
	cache := &DefaultCache{
		cleanUpInterval: 5 * time.Minute,
	}

	// Start a background goroutine to clean expired items
	go cache.cleanupLoop()

	return cache, nil
}

// Get retrieves a value from cache by key
func (c *DefaultCache) Get(ctx context.Context, key string) ([]byte, error) {
	// Check for context cancellation
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}

	value, found := c.items.Load(key)
	if !found {
		return nil, ErrKeyNotFound
	}

	item, ok := value.(cacheItem)
	if !ok {
		return nil, fmt.Errorf("%w: %T", ErrUnexpectedValueType, value)
	}

	// Check if the item has expired
	if !item.expiration.IsZero() && time.Now().UTC().After(item.expiration) {
		c.items.Delete(key)
		return nil, ErrKeyExpired
	}

	return item.value, nil
}

// Set stores a value in cache with optional expiration
func (c *DefaultCache) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	// Check for context cancellation
	if ctx.Err() != nil {
		return ctx.Err()
	}

	var expirationTime time.Time
	if expiration > 0 {
		expirationTime = time.Now().UTC().Add(expiration)
	}

	c.items.Store(key, cacheItem{
		value:      value,
		expiration: expirationTime,
	})

	return nil
}

// Delete removes a key from cache
func (c *DefaultCache) Delete(ctx context.Context, key string) error {
	// Check for context cancellation
	if ctx.Err() != nil {
		return ctx.Err()
	}

	c.items.Delete(key)

	return nil
}

// Exists checks if a key exists in cache
func (c *DefaultCache) Exists(ctx context.Context, key string) (bool, error) {
	// Check for context cancellation
	if ctx.Err() != nil {
		return false, ctx.Err()
	}

	value, found := c.items.Load(key)
	if !found {
		return false, nil
	}

	item, ok := value.(cacheItem)
	if !ok {
		return false, fmt.Errorf("%w: %T", ErrUnexpectedValueType, value)
	}

	// Check if the item has expired
	if !item.expiration.IsZero() && time.Now().UTC().After(item.expiration) {
		c.items.Delete(key)
		return false, nil
	}

	return true, nil
}

// Clear removes all keys from cache
func (c *DefaultCache) Clear(ctx context.Context) error {
	// Check for context cancellation
	if ctx.Err() != nil {
		return ctx.Err()
	}

	c.items = sync.Map{}

	return nil
}

// cleanupLoop periodically removes expired items from the cache to prevent memory leaks.
// This method runs in a background goroutine and uses a ticker to perform cleanup
// at regular intervals defined by cleanUpInterval.
func (c *DefaultCache) cleanupLoop() {
	ticker := time.NewTicker(c.cleanUpInterval)
	defer ticker.Stop()

	// Continuously check for expired items
	for range ticker.C {
		// Iterate through all cache items
		c.items.Range(func(key, value any) bool {
			item, ok := value.(cacheItem)
			if !ok {
				return false // Stop iteration if unexpected type
			}

			// Remove expired items
			if !item.expiration.IsZero() && time.Now().UTC().After(item.expiration) {
				c.items.Delete(key)
			}

			return true // Continue iteration
		})
	}
}
