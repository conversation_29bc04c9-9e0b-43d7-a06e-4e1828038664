package validatorext

import (
	"context"

	cqmModel "sa-intranet/usecase/cqm/model"

	authModel "sa-intranet/usecase/auth/model"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

// CustomValidator holds the database connection for performing validation queries.
// It provides methods for validating model existence and uniqueness constraints.
type CustomValidator struct {
	// DB is the database connection used for validation queries
	DB *bun.DB
}

// ValidateModelExists dynamically checks if a model with the given UUID exists in the database.
// It supports validation for different model types and returns a validator function
// that can be used with the go-playground/validator package.
//
// Parameters:
//   - modelName: The name of the model type to validate (e.g., "CompanyClient", "JiraProject")
//
// Returns a validator function that checks if the UUID field exists in the corresponding table.
func (cv *CustomValidator) ValidateModelExists(modelName string) validator.FuncCtx {
	return func(ctx context.Context, fl validator.FieldLevel) bool {
		// Extract UUID from the field being validated
		id, ok := fl.Field().Interface().(uuid.UUID)
		if !ok {
			return false
		}

		var modelInstance any

		// Map model names to their corresponding struct types
		switch modelName {
		case "CompanyClient":
			modelInstance = &cqmModel.CompanyClient{}
		case "JiraProject":
			modelInstance = &cqmModel.JiraProject{}
		case "User":
			modelInstance = &authModel.User{}
		default:
			return false // Unsupported model type
		}

		// Check if a record with the given ID exists in the database
		exists, err := cv.DB.NewSelect().Model(modelInstance).Where("id = ?", id).Exists(ctx)
		if err != nil {
			// Log or handle database error here
			return false
		}

		return exists
	}
}

// ValidateModelWithKeyAlredyExists validates that a value doesn't already exist in a specific column.
// This is useful for enforcing uniqueness constraints during validation.
//
// Type Parameters:
//   - ColumnType: The type of the column value being validated
//
// Parameters:
//   - db: Database connection for performing the existence check
//   - modelInstance: Instance of the model to check against
//   - columnName: Name of the column to check for uniqueness
//
// Returns a validator function that returns true if the value does NOT exist (validation passes).
func ValidateModelWithKeyAlredyExists[ColumnType any](db *bun.DB, modelInstance any, columnName string) validator.FuncCtx {
	return func(ctx context.Context, fl validator.FieldLevel) bool {
		// Extract the column value from the field being validated
		columnValue, ok := fl.Field().Interface().(ColumnType)
		if !ok {
			return false
		}

		// Check if a record with this column value already exists
		exists, err := db.NewSelect().Model(modelInstance).Where(columnName+" = ?", columnValue).Exists(ctx)
		if err != nil {
			// Log or handle database error here
			return false
		}

		// Return true if the value does NOT exist (validation passes for uniqueness)
		return !exists
	}
}
