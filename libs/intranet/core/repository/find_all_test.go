package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/uptrace/bun"
)

type DummyFilter struct {
	Name string `json:"name"`
}

func TestFindAll(t *testing.T) {
	db := bunDB

	type args struct {
		params    repository.PaginationParams[DummyFilter]
		opts      []repository.FindAllOptionsFunc[DummyFilter]
		setupFunc func(db *bun.DB) error // optional setup function
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *repository.PaginatedResult[DummyModel])
	}{
		{
			name: "successful_find_all",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     1,
					PageSize: 10,
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test successful 1"},
						{Name: "Test successful 2"},
						{Name: "Test successful 3"},
					}
					_, err := db.NewInsert().Model(&models).Exec(context.Background())
					return err
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						names := []string{
							"Test successful 1",
							"Test successful 2",
							"Test successful 3",
						}
						q.Where("name IN (?)", bun.In(names))
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 3 {
					t.Errorf("expected 3 items, got %d", len(result.Items))
				}
				if result.TotalItems != 3 {
					t.Errorf("expected total of 3 items, got %d", result.TotalItems)
				}
				if result.Page != 1 {
					t.Errorf("expected page 1, got %d", result.Page)
				}
			},
		},
		{
			name: "pagination_works",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     2,
					PageSize: 2,
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test Item 1"},
						{Name: "Test Item 2"},
						{Name: "Test Item 3"},
						{Name: "Test Item 4"},
						{Name: "Test Item 5"},
					}
					_, err := db.NewInsert().Model(&models).Exec(context.Background())
					return err
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						names := []string{
							"Test Item 1",
							"Test Item 2",
							"Test Item 3",
							"Test Item 4",
							"Test Item 5",
						}
						q.Where("name IN (?)", bun.In(names))
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()

				if len(result.Items) != 2 {
					t.Errorf("expected 2 items on page 2, got %d", len(result.Items))
				}

				if result.TotalItems != 5 {
					t.Errorf("expected total of 5 items, got %d", result.TotalItems)
				}

				if result.TotalPages != 3 {
					t.Errorf("expected 3 total pages, got %d", result.TotalPages)
				}
			},
		},
		{
			name: "find_with_query_builder",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     1,
					PageSize: 10,
					Filters: DummyFilter{
						Name: "Test 1000",
					},
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						if params.Filters.Name != "" {
							q.Where("name = ?", params.Filters.Name)
						}
					}),
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test 1000"},
						{Name: "Test 2"},
						{Name: "Test 3"},
					}
					_, err := db.NewInsert().
						Model(&models).
						Returning("*").
						Exec(context.Background())
					if err != nil {
						t.Errorf("failed to insert initial records: %v", err)
					}
					return nil
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 1 {
					t.Errorf("expected 1 filtered item, got %d", len(result.Items))
				}
				if result.Items[0].Name != "Test 1000" {
					t.Errorf("expected 'Test 1000', got %s", result.Items[0].Name)
				}
			},
		},
		{
			name: "invalid_pagination_params",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     0, // Invalid page number
					PageSize: 10,
				},
			},
			wantErr: true,
		},
		{
			name: "empty_result",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Filters: DummyFilter{
						Name: "Nonexistent",
					},
					Page:     1,
					PageSize: 10,
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						if params.Filters.Name != "" {
							q.Where("name = ?", params.Filters.Name)
						}
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 0 {
					t.Errorf("expected empty result, got %d items", len(result.Items))
				}
				if result.TotalItems != 0 {
					t.Errorf("expected 0 total items, got %d", result.TotalItems)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Run setup if provided
			if tt.args.setupFunc != nil {
				if err := tt.args.setupFunc(db); err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			result, err := repository.FindAll[DummyModel, DummyFilter](
				context.Background(),
				db,
				tt.args.params,
				tt.args.opts...,
			)

			if (err != nil) != tt.wantErr {
				t.Errorf("FindAll() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, result)
			}
		})
	}
}

func TestNewPaginatedResult(t *testing.T) {
	tests := []struct {
		name       string
		items      []DummyModel
		totalItems int64
		page       int
		pageSize   int
		want       *repository.PaginatedResult[DummyModel]
		verify     func(t *testing.T, got *repository.PaginatedResult[DummyModel], items []DummyModel, totalItems int64, page, pageSize int)
	}{
		{
			name:       "empty_items_nil_slice",
			items:      nil,
			totalItems: 0,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[DummyModel], items []DummyModel, totalItems int64, page, pageSize int) {
				if got.Items == nil {
					t.Error("expected Items to be initialized as empty slice, got nil")
				}
				if len(got.Items) != 0 {
					t.Errorf("expected empty Items slice, got length %d", len(got.Items))
				}
				if got.TotalItems != totalItems {
					t.Errorf("expected TotalItems %d, got %d", totalItems, got.TotalItems)
				}
				if got.TotalPages != 0 {
					t.Errorf("expected TotalPages 0, got %d", got.TotalPages)
				}
				if got.Page != page {
					t.Errorf("expected Page %d, got %d", page, got.Page)
				}
				if got.PageSize != pageSize {
					t.Errorf("expected PageSize %d, got %d", pageSize, got.PageSize)
				}
			},
		},
		{
			name: "with_items_calculates_pages_correctly",
			items: []DummyModel{
				{Name: "Item 1"},
				{Name: "Item 2"},
			},
			totalItems: 25,
			page:       2,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[DummyModel], items []DummyModel, totalItems int64, page, pageSize int) {
				if len(got.Items) != len(items) {
					t.Errorf("expected Items length %d, got %d", len(items), len(got.Items))
				}
				if got.TotalItems != totalItems {
					t.Errorf("expected TotalItems %d, got %d", totalItems, got.TotalItems)
				}
				expectedPages := 3 // 25 items / 10 per page = 3 pages
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
				if got.Page != page {
					t.Errorf("expected Page %d, got %d", page, got.Page)
				}
				if got.PageSize != pageSize {
					t.Errorf("expected PageSize %d, got %d", pageSize, got.PageSize)
				}
			},
		},
		{
			name: "exact_division_pages",
			items: []DummyModel{
				{Name: "Item 1"},
			},
			totalItems: 20,
			page:       1,
			pageSize:   10,
			verify: func(t *testing.T, got *repository.PaginatedResult[DummyModel], items []DummyModel, totalItems int64, page, pageSize int) {
				expectedPages := 2 // 20 items / 10 per page = 2 pages exactly
				if got.TotalPages != expectedPages {
					t.Errorf("expected TotalPages %d, got %d", expectedPages, got.TotalPages)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := repository.NewPaginatedResult(tt.items, tt.totalItems, tt.page, tt.pageSize)

			if tt.verify != nil {
				tt.verify(t, got, tt.items, tt.totalItems, tt.page, tt.pageSize)
			}
		})
	}
}

func TestPaginatedResultIsEmpty(t *testing.T) {
	tests := []struct {
		name   string
		result *repository.PaginatedResult[DummyModel]
		want   bool
	}{
		{
			name: "empty_result",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{},
			},
			want: true,
		},
		{
			name: "nil_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: nil,
			},
			want: true,
		},
		{
			name: "with_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{{Name: "Test"}},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.result.IsEmpty()
			if got != tt.want {
				t.Errorf("PaginatedResult.IsEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPaginatedResultFirst(t *testing.T) {
	tests := []struct {
		name     string
		result   *repository.PaginatedResult[DummyModel]
		wantItem DummyModel
		wantOk   bool
	}{
		{
			name: "empty_result",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{},
			},
			wantItem: DummyModel{},
			wantOk:   false,
		},
		{
			name: "nil_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: nil,
			},
			wantItem: DummyModel{},
			wantOk:   false,
		},
		{
			name: "single_item",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{{Name: "First"}},
			},
			wantItem: DummyModel{Name: "First"},
			wantOk:   true,
		},
		{
			name: "multiple_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{
					{Name: "First"},
					{Name: "Second"},
					{Name: "Third"},
				},
			},
			wantItem: DummyModel{Name: "First"},
			wantOk:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotItem, gotOk := tt.result.First()
			if gotOk != tt.wantOk {
				t.Errorf("PaginatedResult.First() ok = %v, want %v", gotOk, tt.wantOk)
			}
			if gotItem.Name != tt.wantItem.Name {
				t.Errorf("PaginatedResult.First() item = %v, want %v", gotItem, tt.wantItem)
			}
		})
	}
}

func TestPaginatedResultLast(t *testing.T) {
	tests := []struct {
		name     string
		result   *repository.PaginatedResult[DummyModel]
		wantItem DummyModel
		wantOk   bool
	}{
		{
			name: "empty_result",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{},
			},
			wantItem: DummyModel{},
			wantOk:   false,
		},
		{
			name: "nil_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: nil,
			},
			wantItem: DummyModel{},
			wantOk:   false,
		},
		{
			name: "single_item",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{{Name: "Only"}},
			},
			wantItem: DummyModel{Name: "Only"},
			wantOk:   true,
		},
		{
			name: "multiple_items",
			result: &repository.PaginatedResult[DummyModel]{
				Items: []DummyModel{
					{Name: "First"},
					{Name: "Second"},
					{Name: "Last"},
				},
			},
			wantItem: DummyModel{Name: "Last"},
			wantOk:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotItem, gotOk := tt.result.Last()
			if gotOk != tt.wantOk {
				t.Errorf("PaginatedResult.Last() ok = %v, want %v", gotOk, tt.wantOk)
			}
			if gotItem.Name != tt.wantItem.Name {
				t.Errorf("PaginatedResult.Last() item = %v, want %v", gotItem, tt.wantItem)
			}
		})
	}
}
