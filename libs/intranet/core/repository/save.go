package repository

import (
	"context"
	"errors"
	"fmt"
	"log/slog"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect"
)

// Define static errors
var (
	ErrModelNil       = errors.New("model cannot be nil")
	ErrNoRowsAffected = errors.New("no rows affected")
)

// SaveOptions configures the behavior of Save operations.
// It supports different save modes (insert, update, upsert) and field-level control.
type SaveOptions struct {
	// IDColumnName specifies the primary key column name (default: "id")
	IDColumnName string
	// IsUpsert determines if the operation should insert or update based on existence
	IsUpsert bool
	// IsNew indicates this is a new record that should be inserted
	IsNew bool
	// UpdateFields specifies which fields to update (empty means all fields)
	UpdateFields []string
	// AllowUpdateFields restricts which fields are allowed to be updated
	AllowUpdateFields []string
	// Base contains common query building options
	Base BaseOptions
}

// SaveOptionsFunc is a function type for configuring SaveOptions using the functional options pattern.
type SaveOptionsFunc func(opts *SaveOptions)

// WithSaveAllowUpdateFields restricts which fields can be updated during save operations.
// This provides security by preventing unauthorized field modifications.
func WithSaveAllowUpdateFields(fields []string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.AllowUpdateFields = fields
	}
}

// WithSaveUpdateFields specifies exactly which fields should be updated.
// If empty, all fields will be updated (subject to AllowUpdateFields restrictions).
func WithSaveUpdateFields(fields []string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.UpdateFields = fields
	}
}

func WithSaveUpsert(isUpsert bool) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IsUpsert = isUpsert
	}
}

func WithSaveNew(isNew bool) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IsNew = isNew
	}
}

func WithSaveIDColumnName(idColumnName string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IDColumnName = idColumnName
	}
}

// Save persists a model to the database using insert, update, or upsert operations.
// The operation type is determined by the SaveOptions configuration.
//
// Type Parameters:
//   - T: The model type to save
//
// Parameters:
//   - ctx: Context for the database operation
//   - db: Database connection interface
//   - model: Pointer to the model to save (cannot be nil)
//   - opts: Optional configuration functions
//
// Returns an error if the save operation fails or if model is nil.
func Save[T any](ctx context.Context, db bun.IDB, model *T, opts ...SaveOptionsFunc) error {
	// Initialize default options for new record insertion
	options := &SaveOptions{
		IDColumnName: "id",
		IsNew:        true,
		IsUpsert:     false,
		UpdateFields: []string{},
	}

	// Validate input
	if model == nil {
		return ErrModelNil
	}

	// Apply functional options
	for _, opt := range opts {
		opt(options)
	}

	// Route to appropriate save method based on configuration
	if options.IsNew || options.IsUpsert {
		return saveInsert(ctx, db, model, options)
	}

	return saveUpdate(ctx, db, model, options)
}

func saveInsert[T any](ctx context.Context, db bun.IDB, model *T, options *SaveOptions) error {
	inserQuery := db.NewInsert().Model(model)

	if options.IsUpsert {
		inserQuery = handleUpsert(db, inserQuery, options.IDColumnName)
	}

	_, err := inserQuery.
		Returning("*").
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to insert model: %w", err)
	}

	return nil
}

func handleUpsert(db bun.IDB, query *bun.InsertQuery, idColumnName string) *bun.InsertQuery {
	dialectName := db.Dialect().Name()
	switch dialectName {
	case dialect.PG:
		return query.On(fmt.Sprintf("CONFLICT (%s) DO UPDATE SET EXCLUDED.*", idColumnName))
	case dialect.SQLite:
		return query.Replace()
	case dialect.MySQL, dialect.MSSQL, dialect.Oracle:
		return query
	case dialect.Invalid:
		slog.Warn(fmt.Sprintf("Invalid dialect for upsert: %s", dialectName))
		return query
	default:
		slog.Warn(fmt.Sprintf("Unsupported dialect for upsert: %s", dialectName))
		return query
	}
}

func saveUpdate[T any](ctx context.Context, db bun.IDB, model *T, options *SaveOptions) error {
	updateQuery := db.NewUpdate().
		Model(model).
		WherePK().
		Returning("*")

	if len(options.UpdateFields) > 0 && len(options.AllowUpdateFields) > 0 {
		updateQuery = applyAllowedFields(updateQuery, options)
	}

	res, err := updateQuery.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update model: %w", err)
	}

	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrNoRowsAffected
	}

	return nil
}

func applyAllowedFields(query *bun.UpdateQuery, options *SaveOptions) *bun.UpdateQuery {
	allowedMap := make(map[string]bool)
	for _, field := range options.AllowUpdateFields {
		allowedMap[field] = true
	}

	var allowedFields []string

	for _, field := range options.UpdateFields {
		if allowedMap[field] {
			allowedFields = append(allowedFields, field)
		}
	}

	if len(allowedFields) > 0 {
		return query.Column(allowedFields...)
	}

	return query
}
