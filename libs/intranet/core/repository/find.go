package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

// FindOptions configures the behavior of Find operations.
// It allows customization of the ID column name and query building behavior.
type FindOptions struct {
	// IDColumnName specifies the column name used for ID-based lookups (default: "id")
	IDColumnName string
	// Base contains common query building options
	Base BaseOptions
}

// FindOptionsFunc is a function type for configuring FindOptions using the functional options pattern.
type FindOptionsFunc func(opts *FindOptions)

// WithFindIDColumnName sets a custom ID column name for Find operations.
// This is useful when the primary key column is not named "id".
func WithFindIDColumnName(idColumnName string) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.IDColumnName = idColumnName
	}
}

// WithQueryBuilder sets a custom query builder function for Find operations.
// This allows adding custom WHERE clauses, JOINs, or other query modifications.
func WithQueryBuilder(queryBuilderFunc QueryBuilderFunc) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.Base.QueryBuilderFunc = queryBuilderFunc
	}
}

// WithDefaultQueryBuilder sets a default query builder that applies to all Find operations.
// This is useful for adding common filters like soft delete checks.
func WithDefaultQueryBuilder(defaultQueryBuilderFunc QueryBuilderFunc) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.Base.DefaultQueryBuilderFunc = defaultQueryBuilderFunc
	}
}

// Find retrieves a single record by ID using generic type T.
// It supports customizable query building and flexible ID column naming.
//
// Type Parameters:
//   - T: The model type to retrieve
//
// Parameters:
//   - ctx: Context for the database operation
//   - db: Database connection interface
//   - id: UUID of the record to find
//   - opts: Optional configuration functions
//
// Returns a pointer to the found model or an error if not found or on database error.
func Find[T any](ctx context.Context, db bun.IDB, id uuid.UUID, opts ...FindOptionsFunc) (*T, error) {
	model := new(T)

	// Initialize default options
	options := &FindOptions{
		IDColumnName: "id",
	}

	// Apply functional options
	for _, opt := range opts {
		opt(options)
	}

	// Build the base query
	query := db.NewSelect().Model(model)

	// Apply default query modifications (e.g., soft delete filters)
	if options.Base.DefaultQueryBuilderFunc != nil {
		options.Base.DefaultQueryBuilderFunc(query)
	}

	// Apply custom query modifications
	if options.Base.QueryBuilderFunc != nil {
		options.Base.QueryBuilderFunc(query)
	}

	// Execute the query with ID filter
	err := query.
		Where(fmt.Sprintf("%s = ?", options.IDColumnName), id).
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return model, nil
}
