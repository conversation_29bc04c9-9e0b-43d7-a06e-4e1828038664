// Package repository provides generic database operations and query building utilities.
// It implements common CRUD operations using the Bun ORM with support for customizable
// query building and flexible options for different database operations.
package repository

import (
	"github.com/uptrace/bun"
)

// Type definitions for query builder functions that allow customization of database queries.
type (
	// QueryBuilderFunc allows customization of SELECT queries by modifying the query object
	QueryBuilderFunc func(query *bun.SelectQuery)
	// DeleteQueryBuilderFunc allows customization of DELETE queries by modifying the query object
	DeleteQueryBuilderFunc func(query *bun.DeleteQuery)
)

// BaseOptions provides common configuration options for repository operations.
// These options allow customization of query behavior across different repository methods.
type BaseOptions struct {
	// QueryBuilderFunc applies custom modifications to queries
	QueryBuilderFunc QueryBuilderFunc
	// DefaultQueryBuilderFunc applies default modifications to all queries
	DefaultQueryBuilderFunc QueryBuilderFunc
}
