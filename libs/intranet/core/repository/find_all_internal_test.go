package repository

import (
	"reflect"
	"testing"
)

func TestFilterAllowedFields(t *testing.T) {
	tests := []struct {
		name            string
		requestedFields []string
		allowedFields   []string
		want            []string
		verify          func(t *testing.T, got []string, requestedFields, allowedFields []string)
	}{
		{
			name:            "empty_requested_fields",
			requestedFields: []string{},
			allowedFields:   []string{"id", "name", "email"},
			want:            []string{},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != 0 {
					t.<PERSON>rrorf("Expected empty result for empty requested fields, got %v", got)
				}
			},
		},
		{
			name:            "empty_allowed_fields",
			requestedFields: []string{"id", "name", "email"},
			allowedFields:   []string{},
			want:            []string{},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != 0 {
					t.<PERSON><PERSON><PERSON>("Expected empty result for empty allowed fields, got %v", got)
				}
			},
		},
		{
			name:            "all_fields_allowed",
			requestedFields: []string{"id", "name", "email"},
			allowedFields:   []string{"id", "name", "email", "phone"},
			want:            []string{"id", "name", "email"},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != len(requestedFields) {
					t.Errorf("Expected all requested fields to be allowed, got %v", got)
				}
				for _, field := range requestedFields {
					found := false
					for _, gotField := range got {
						if gotField == field {
							found = true
							break
						}
					}
					if !found {
						t.Errorf("Expected field %s to be in result, but it wasn't", field)
					}
				}
			},
		},
		{
			name:            "partial_fields_allowed",
			requestedFields: []string{"id", "name", "password", "email"},
			allowedFields:   []string{"id", "name", "email"},
			want:            []string{"id", "name", "email"},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				expectedFields := []string{"id", "name", "email"}
				if len(got) != len(expectedFields) {
					t.Errorf("Expected %d fields, got %d: %v", len(expectedFields), len(got), got)
				}
				for _, expectedField := range expectedFields {
					found := false
					for _, gotField := range got {
						if gotField == expectedField {
							found = true
							break
						}
					}
					if !found {
						t.Errorf("Expected field %s to be in result, but it wasn't", expectedField)
					}
				}
				// Ensure password is not included
				for _, gotField := range got {
					if gotField == "password" {
						t.Errorf("Field 'password' should not be in result, but it was")
					}
				}
			},
		},
		{
			name:            "no_fields_allowed",
			requestedFields: []string{"password", "secret", "token"},
			allowedFields:   []string{"id", "name", "email"},
			want:            []string{},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != 0 {
					t.Errorf("Expected no fields to be allowed, got %v", got)
				}
			},
		},
		{
			name:            "duplicate_requested_fields",
			requestedFields: []string{"id", "name", "id", "email", "name"},
			allowedFields:   []string{"id", "name", "email"},
			want:            []string{"id", "name", "id", "email", "name"},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				// Should preserve duplicates in the order they were requested
				expected := []string{"id", "name", "id", "email", "name"}
				if len(got) != len(expected) {
					t.Errorf("Expected %d fields (with duplicates), got %d: %v", len(expected), len(got), got)
				}
				for i, expectedField := range expected {
					if i >= len(got) || got[i] != expectedField {
						t.Errorf("Expected field at position %d to be %s, got %s", i, expectedField, got[i])
					}
				}
			},
		},
		{
			name:            "case_sensitive_matching",
			requestedFields: []string{"ID", "Name", "EMAIL"},
			allowedFields:   []string{"id", "name", "email"},
			want:            []string{},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != 0 {
					t.Errorf("Expected no fields to match due to case sensitivity, got %v", got)
				}
			},
		},
		{
			name:            "mixed_case_exact_match",
			requestedFields: []string{"userId", "userName", "userEmail"},
			allowedFields:   []string{"userId", "userName", "userEmail", "userPassword"},
			want:            []string{"userId", "userName", "userEmail"},
			verify: func(t *testing.T, got []string, requestedFields, allowedFields []string) {
				if len(got) != 3 {
					t.Errorf("Expected 3 fields to match exactly, got %d: %v", len(got), got)
				}
				for _, field := range []string{"userId", "userName", "userEmail"} {
					found := false
					for _, gotField := range got {
						if gotField == field {
							found = true
							break
						}
					}
					if !found {
						t.Errorf("Expected field %s to be in result, but it wasn't", field)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := filterAllowedFields(tt.requestedFields, tt.allowedFields)
			
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("filterAllowedFields() = %v, want %v", got, tt.want)
			}
			
			// Custom verification if provided
			if tt.verify != nil {
				tt.verify(t, got, tt.requestedFields, tt.allowedFields)
			}
		})
	}
}
