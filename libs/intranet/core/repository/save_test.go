package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/uptrace/bun"
)

func TestSave(t *testing.T) {
	db := bunDB
	type args struct {
		model     *DummyModel
		opts      []repository.SaveOptionsFunc
		setupFunc func(db *bun.DB) error // optional setup function
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *bun.DB, *DummyModel) // verification function
	}{
		{
			name: "successful_insert",
			args: args{
				model: &DummyModel{
					Name: "Test Dummy",
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				// Verify the record was saved
				var saved DummyModel
				err := db.NewSelect().Model(&saved).Where("id = ?", model.ID).Scan(context.Background())
				if err != nil {
					t.<PERSON>("failed to fetch saved record: %v", err)
				}

				if saved.Name != model.Name {
					t.Errorf("got name %v, want %v", saved.Name, model.Name)
				}
			},
		},
		{
			name: "nil_model",
			args: args{
				model: nil,
			},
			wantErr: true,
		},
		{
			name: "upsert_existing_record",
			args: args{
				model: &DummyModel{
					ID:   "existing-id",
					Name: "Test Dummy",
				},
				opts: []repository.SaveOptionsFunc{
					repository.WithSaveUpsert(true),
				},
				setupFunc: func(db *bun.DB) error {
					// Insert initial record
					_, err := db.NewInsert().
						Model(&DummyModel{
							ID:   "existing-id",
							Name: "Original Name",
						}).
						Returning("*").
						Exec(context.Background())
					if err != nil {
						t.Errorf("failed to insert initial record: %v", err)
					}
					return nil
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				var count int
				count, err := db.NewSelect().
					Model(model).
					Where("id = ?", model.ID).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}

				if count != 1 {
					t.Errorf("got %d records, want 1", count)
				}
			},
		},
		{
			name: "save_with_custom_id_column",
			args: args{
				model: &DummyModel{
					Name: "Custom ID Test",
				},
				opts: []repository.SaveOptionsFunc{
					repository.WithSaveIDColumnName("id"),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				if model.ID == "" {
					t.Error("IDwas not set")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Run setup if provided
			if tt.args.setupFunc != nil {
				if err := tt.args.setupFunc(db); err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			err := repository.Save(context.Background(), db, tt.args.model, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, tt.args.model)
			}
		})
	}
}

func TestWithSaveAllowUpdateFields(t *testing.T) {
	db := bunDB
	tests := []struct {
		name         string
		allowFields  []string
		updateFields []string
		setupFunc    func(db *bun.DB) (*DummyModel, error)
		wantErr      bool
		verify       func(t *testing.T, db *bun.DB, model *DummyModel)
	}{
		{
			name:         "allow_specific_fields",
			allowFields:  []string{"name"},
			updateFields: []string{"name"},
			setupFunc: func(db *bun.DB) (*DummyModel, error) {
				model := &DummyModel{
					Name: "Original Name",
				}
				_, err := db.NewInsert().Model(model).Exec(context.Background())
				return model, err
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()
				var retrieved DummyModel
				err := db.NewSelect().
					Model(&retrieved).
					Where("id = ?", model.ID).
					Scan(context.Background())
				if err != nil {
					t.Errorf("failed to retrieve model: %v", err)
				}
				if retrieved.Name != "Updated Name" {
					t.Errorf("expected name 'Updated Name', got %s", retrieved.Name)
				}
			},
		},
		{
			name:         "restrict_fields",
			allowFields:  []string{"name"},
			updateFields: []string{"name", "id"}, // id should be filtered out
			setupFunc: func(db *bun.DB) (*DummyModel, error) {
				model := &DummyModel{
					Name: "Original Name",
				}
				_, err := db.NewInsert().Model(model).Exec(context.Background())
				return model, err
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()
				// Verify the model was updated (only allowed fields)
				var retrieved DummyModel
				err := db.NewSelect().
					Model(&retrieved).
					Where("id = ?", model.ID).
					Scan(context.Background())
				if err != nil {
					t.Errorf("failed to retrieve model: %v", err)
				}
				if retrieved.Name != "Updated Name" {
					t.Errorf("expected name 'Updated Name', got %s", retrieved.Name)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var model *DummyModel
			var err error

			if tt.setupFunc != nil {
				model, err = tt.setupFunc(db)
				if err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			// Update the model
			model.Name = "Updated Name"

			opts := []repository.SaveOptionsFunc{
				repository.WithSaveNew(false), // Update mode
				repository.WithSaveAllowUpdateFields(tt.allowFields),
				repository.WithSaveUpdateFields(tt.updateFields),
			}

			err = repository.Save(context.Background(), db, model, opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() with allow update fields error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, model)
			}
		})
	}
}

func TestWithSaveUpdateFields(t *testing.T) {
	db := bunDB
	tests := []struct {
		name         string
		updateFields []string
		setupFunc    func(db *bun.DB) (*DummyModel, error)
		wantErr      bool
		verify       func(t *testing.T, db *bun.DB, model *DummyModel)
	}{
		{
			name:         "update_specific_fields",
			updateFields: []string{"name"},
			setupFunc: func(db *bun.DB) (*DummyModel, error) {
				model := &DummyModel{
					Name: "Original Name",
				}
				_, err := db.NewInsert().Model(model).Exec(context.Background())
				return model, err
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()
				var retrieved DummyModel
				err := db.NewSelect().
					Model(&retrieved).
					Where("id = ?", model.ID).
					Scan(context.Background())
				if err != nil {
					t.Errorf("failed to retrieve model: %v", err)
				}
				if retrieved.Name != "Updated Name" {
					t.Errorf("expected name 'Updated Name', got %s", retrieved.Name)
				}
			},
		},
		{
			name:         "empty_update_fields",
			updateFields: []string{},
			setupFunc: func(db *bun.DB) (*DummyModel, error) {
				model := &DummyModel{
					Name: "Original Name",
				}
				_, err := db.NewInsert().Model(model).Exec(context.Background())
				return model, err
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()
				var retrieved DummyModel
				err := db.NewSelect().
					Model(&retrieved).
					Where("id = ?", model.ID).
					Scan(context.Background())
				if err != nil {
					t.Errorf("failed to retrieve model: %v", err)
				}
				if retrieved.Name != "Updated Name" {
					t.Errorf("expected name 'Updated Name', got %s", retrieved.Name)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var model *DummyModel
			var err error

			if tt.setupFunc != nil {
				model, err = tt.setupFunc(db)
				if err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			// Update the model
			model.Name = "Updated Name"

			opts := []repository.SaveOptionsFunc{
				repository.WithSaveNew(false), // Update mode
				repository.WithSaveUpdateFields(tt.updateFields),
			}

			err = repository.Save(context.Background(), db, model, opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() with update fields error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, model)
			}
		})
	}
}

func TestWithSaveNew(t *testing.T) {
	db := bunDB
	tests := []struct {
		name    string
		isNew   bool
		model   *DummyModel
		wantErr bool
		verify  func(t *testing.T, db *bun.DB, model *DummyModel)
	}{
		{
			name:  "save_new_true",
			isNew: true,
			model: &DummyModel{
				Name: "New Model",
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()
				if model.ID == "" {
					t.Error("expected ID to be set after insert")
				}
				var count int
				count, err := db.NewSelect().
					Model((*DummyModel)(nil)).
					Where("id = ?", model.ID).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}
				if count != 1 {
					t.Errorf("expected 1 record, got %d", count)
				}
			},
		},
		{
			name:  "save_new_false",
			isNew: false,
			model: &DummyModel{
				ID:   "test-id",
				Name: "Existing Model",
			},
			wantErr: true, // Should fail because record doesn't exist for update
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			opts := []repository.SaveOptionsFunc{
				repository.WithSaveNew(tt.isNew),
			}

			err := repository.Save(context.Background(), db, tt.model, opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() with new flag error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, tt.model)
			}
		})
	}
}
