package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

type DeleteOptions struct {
	IDColumnName            string
	QueryBuilderFunc        DeleteQueryBuilderFunc
	DefaultQueryBuilderFunc DeleteQueryBuilderFunc
}

type DeleteOptionsFunc func(opts *DeleteOptions)

func WithDeleteIDColumnName(idColumnName string) DeleteOptionsFunc {
	return func(opts *DeleteOptions) {
		opts.IDColumnName = idColumnName
	}
}

func WithDeleteQueryBuilder(queryBuilderFunc DeleteQueryBuilderFunc) DeleteOptionsFunc {
	return func(opts *DeleteOptions) {
		opts.QueryBuilderFunc = queryBuilderFunc
	}
}

func WithDeleteDefaultQueryBuilder(defaultQueryBuilderFunc DeleteQueryBuilderFunc) DeleteOptionsFunc {
	return func(opts *DeleteOptions) {
		opts.DefaultQueryBuilderFunc = defaultQueryBuilderFunc
	}
}

// Delete removes a model from the database by ID using generic type T.
// It supports customizable query building for complex delete operations.
//
// Type Parameters:
//   - T: The model type to delete
//
// Parameters:
//   - ctx: Context for the database operation
//   - db: Database connection interface
//   - id: UUID of the record to delete
//   - opts: Optional configuration functions
//
// Returns an error if the delete operation fails or if no rows are affected.
func Delete[T any](ctx context.Context, db bun.IDB, id uuid.UUID, opts ...DeleteOptionsFunc) error {
	// Initialize default options
	options := &DeleteOptions{
		IDColumnName: "id",
	}

	// Apply functional options
	for _, opt := range opts {
		opt(options)
	}

	// Initialize delete query for the model type
	query := db.NewDelete().Model((*T)(nil))

	// Apply default query modifications (e.g., soft delete conditions)
	if options.DefaultQueryBuilderFunc != nil {
		options.DefaultQueryBuilderFunc(query)
	}

	// Apply custom query modifications
	if options.QueryBuilderFunc != nil {
		options.QueryBuilderFunc(query)
	}

	// Execute delete operation with ID filter
	res, err := query.Where(fmt.Sprintf("%s = ?", options.IDColumnName), id).Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete model: %w", err)
	}

	// Check if any rows were affected
	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrNoRowsAffected
	}

	return nil
}
