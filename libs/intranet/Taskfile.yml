# https://taskfile.dev

version: '3'

tasks:
  run-sonar-scanner-prepare:
    cmds:
      # https://dustinspecker.com/posts/go-combined-unit-integration-code-coverage/
      
      - go vet ./... 2> coverage/govet-report.out || true
      - golangci-lint run --output.checkstyle.path=${PWD}/coverage/golangci-lint-report.xml --show-stats=false --issues-exit-code 0 ./... || true
      - rm -rf coverage/data coverage/profile.out
      - mkdir -p coverage/data
      # - go test -json -failfast -covermode=atomic -count=1 -v -timeout 30s -cover  ./... -args -test.gocoverdir="${PWD}/coverage/data" > coverage/tests-report.json
      - gotestsum --format-hide-empty-pkg --debug --format testdox --jsonfile coverage/tests-report.json -- -failfast -covermode=atomic -count=1 -v -timeout 30s -cover  $(go list ./... | grep -Ev "sa-intranet/db/migrate|sa-intranet/cli") -coverpkg=$(go list -f '{{ if .TestGoFiles }}{{ .ImportPath }}{{ end }}' ./... | paste -sd "," -) -args -test.gocoverdir="${PWD}/coverage/data" 
      # - go test -failfast -covermode=atomic -v -count=1 -v -timeout 30s -json  > coverage/tests-report.json ./... || true 
      - go tool covdata textfmt -i=./coverage/data -o coverage/profile.out || true
      - gocov convert coverage/profile.out | gocov-html > coverage/coverage-pretty.html
      - go tool cover -func=coverage/profile.out
      # - go list ./... | paste -sd,
      # - go tool covdata textfmt -i=./coverage/data -o coverage/profile.out -pkg="$(go list ./... | grep -v -f .coverignore | tr '\n' ',' | sed 's/,$//')" || true
      
      # - INCLUDE_FILES="$(grep -Eo "^$(go list . | sed 's|.*/||')/[^:]+" coverage/profile.out | sed "s|^$(go list . | sed 's|.*/||')/||" | sort -u | paste -sd ',' -)"; sed -i '/^sonar.inclusions=/c\'"sonar.inclusions=$INCLUDE_FILES" sonar-project.properties
      # - EXCLUDE_PACKAGES="sonar.coverage.exclusions=**/vendor/**,**/tmp/**,**/testdata/**,$(MOD=$(go list .); go list ./... | while read pkg; do short=${pkg#$MOD/}; if ! go tool covdata percent -i=./coverage/data | awk '{print $1}' | grep -q "^$pkg"; then if ! go tool covdata percent -i=./coverage/data | awk '{print $1}' | grep -q "^$pkg/"; then echo "**/$short/**"; fi; fi; done | sort -u | paste -sd, -;)"; sed -i '/^sonar.coverage.exclusions=/c\'"$EXCLUDE_PACKAGES" sonar-project.properties
    silent: false
  run-security-scan:
    cmds:
    - govulncheck -show verbose ./...
    silent: false
