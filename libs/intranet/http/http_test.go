package http

import (
	"log/slog"
	"os"
	"testing"
)

func TestMain(m *testing.M) {
	// Run all tests
	if code := m.Run(); code != 0 {
		slog.Error("test suite failed with:", "code", code)
	}
}

func TestNewApp(t *testing.T) {
	tests := []struct {
		name    string
		envVars map[string]string
		verify  func(t *testing.T, app *App)
	}{
		{
			name:    "default_configuration",
			envVars: map[string]string{},
			verify: func(t *testing.T, app *App) {
				if app == nil {
					t.Error("expected app to be created, got nil")
					return
				}
				if app.Config.BaseURL != "http://localhost:8000" {
					t.Errorf("expected default BaseURL 'http://localhost:8000', got %s", app.Config.BaseURL)
				}
				if app.DefaultApp == nil {
					t.Error("expected DefaultApp to be initialized")
				}
			},
		},
		{
			name: "custom_base_url_from_env",
			envVars: map[string]string{
				"BASE_URL": "https://example.com:9000",
			},
			verify: func(t *testing.T, app *App) {
				if app == nil {
					t.Error("expected app to be created, got nil")
					return
				}
				if app.Config.BaseURL != "https://example.com:9000" {
					t.Errorf("expected BaseURL 'https://example.com:9000', got %s", app.Config.BaseURL)
				}
			},
		},
		{
			name: "empty_base_url_uses_default",
			envVars: map[string]string{
				"BASE_URL": "",
			},
			verify: func(t *testing.T, app *App) {
				if app == nil {
					t.Error("expected app to be created, got nil")
					return
				}
				// Empty env var should still use default
				if app.Config.BaseURL != "http://localhost:8000" {
					t.Errorf("expected default BaseURL when empty env var, got %s", app.Config.BaseURL)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables for test
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			// Clean up environment variables after test
			defer func() {
				for key := range tt.envVars {
					os.Unsetenv(key)
				}
			}()

			// Test NewApp
			app := NewApp()

			// Run verification
			if tt.verify != nil {
				tt.verify(t, app)
			}
		})
	}
}

func TestNewAppPanicHandling(t *testing.T) {
	// Test that NewApp handles configuration errors gracefully
	// This test verifies that missing .env files don't cause panics

	// Change to a directory that doesn't have .env file
	originalDir, _ := os.Getwd()
	tempDir := t.TempDir()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// This should not panic even without .env file
	app := NewApp()
	if app == nil {
		t.Error("expected app to be created even without .env file")
	}

	// Should use default configuration
	if app.Config.BaseURL != "http://localhost:8000" {
		t.Errorf("expected default BaseURL without .env file, got %s", app.Config.BaseURL)
	}
}

func TestAppStructure(t *testing.T) {
	app := NewApp()

	// Test that all required fields are initialized
	if app.DefaultApp == nil {
		t.Error("DefaultApp should be initialized")
	}

	// Test that Config is properly set
	if app.Config.BaseURL == "" {
		t.Error("Config.BaseURL should not be empty")
	}
}
