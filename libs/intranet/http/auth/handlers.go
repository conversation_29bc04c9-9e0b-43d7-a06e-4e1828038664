// Package auth provides HTTP authentication handlers for the SA Intranet application.
// It includes logout functionality with proper session cleanup and AWS Cognito integration.
package auth

import (
	"net/http"
	"time"
)

// LogoutHandler creates an HTTP handler that performs user logout by clearing
// AWS ELB authentication session cookies and redirecting to the Cognito logout URL.
// This ensures complete session cleanup for users authenticated through AWS Cognito.
//
// Parameters:
//   - logOutURL: The AWS Cognito logout URL to redirect users after session cleanup
//
// Returns an HTTP handler that can be registered with a router for logout endpoints.
func LogoutHandler(logOutURL string) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		logoutURL := logOutURL

		// Clear AWS ELB authentication session cookies to ensure complete logout
		// These cookies are automatically set by AWS Application Load Balancer
		// when using Cognito authentication integration

		// Clear the primary authentication session cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "AWSELBAuthSessionCookie-0",
			Value:    "",
			Path:     "/",
			Expires:  time.Unix(0, 0), // Set to epoch time to expire immediately
			HttpOnly: true,
		})

		// Clear the secondary authentication session cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "AWSELBAuthSessionCookie-1",
			Value:    "",
			Path:     "/",
			Expires:  time.Unix(0, 0), // Set to epoch time to expire immediately
			HttpOnly: true,
		})

		// Redirect the user to the AWS Cognito logout URL for complete session termination
		http.Redirect(w, r, logoutURL, http.StatusSeeOther)
	})
}
