package auth_test

import (
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"sa-intranet/http/auth"
)

func TestMain(m *testing.M) {
	// Run all tests
	if code := m.Run(); code != 0 {
		slog.Error("test suite failed with:", "code", code)
	}
}

func TestLogoutHandler(t *testing.T) {
	tests := []struct {
		name       string
		logOutURL  string
		wantStatus int
		verify     func(t *testing.T, resp *httptest.ResponseRecorder, logOutURL string)
	}{
		{
			name:       "successful_logout_with_cognito_url",
			logOutURL:  "https://cognito.amazonaws.com/logout",
			wantStatus: http.StatusSeeOther,
			verify: func(t *testing.T, resp *httptest.ResponseRecorder, logOutURL string) {
				// Check redirect location
				location := resp.Header().Get("Location")
				if location != logOutURL {
					t.Errorf("expected redirect to %s, got %s", logOutURL, location)
				}

				// Check that AWS ELB cookies are cleared
				cookies := resp.Result().Cookies()
				cookieNames := []string{"AWSELBAuthSessionCookie-0", "AWSELBAuthSessionCookie-1"}

				for _, expectedCookieName := range cookieNames {
					found := false
					for _, cookie := range cookies {
						if cookie.Name == expectedCookieName {
							found = true
							if cookie.Value != "" {
								t.Errorf("expected cookie %s to be empty, got %s", expectedCookieName, cookie.Value)
							}
							if cookie.Path != "/" {
								t.Errorf("expected cookie %s path to be '/', got %s", expectedCookieName, cookie.Path)
							}
							if !cookie.Expires.Equal(time.Unix(0, 0)) {
								t.Errorf("expected cookie %s to be expired, got %v", expectedCookieName, cookie.Expires)
							}
							if !cookie.HttpOnly {
								t.Errorf("expected cookie %s to be HttpOnly", expectedCookieName)
							}
							break
						}
					}
					if !found {
						t.Errorf("expected cookie %s to be set for clearing", expectedCookieName)
					}
				}
			},
		},
		{
			name:       "logout_with_empty_url",
			logOutURL:  "",
			wantStatus: http.StatusSeeOther,
			verify: func(t *testing.T, resp *httptest.ResponseRecorder, logOutURL string) {
				// Should still redirect even with empty URL
				location := resp.Header().Get("Location")
				// When URL is empty, http.Redirect redirects to "/"
				if location != "/" {
					t.Errorf("expected redirect to '/', got %s", location)
				}
			},
		},
		{
			name:       "logout_with_custom_url",
			logOutURL:  "https://example.com/custom-logout",
			wantStatus: http.StatusSeeOther,
			verify: func(t *testing.T, resp *httptest.ResponseRecorder, logOutURL string) {
				location := resp.Header().Get("Location")
				if location != logOutURL {
					t.Errorf("expected redirect to %s, got %s", logOutURL, location)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create handler
			handler := auth.LogoutHandler(tt.logOutURL)

			// Create test request
			req := httptest.NewRequest(http.MethodGet, "/logout", nil)
			resp := httptest.NewRecorder()

			// Execute handler
			handler.ServeHTTP(resp, req)

			// Check status code
			if resp.Code != tt.wantStatus {
				t.Errorf("expected status %d, got %d", tt.wantStatus, resp.Code)
			}

			// Run custom verification
			if tt.verify != nil {
				tt.verify(t, resp, tt.logOutURL)
			}
		})
	}
}

func TestLogoutHandlerIntegration(t *testing.T) {
	// Test that the handler works correctly when integrated with a router
	logOutURL := "https://cognito.amazonaws.com/logout"
	handler := auth.LogoutHandler(logOutURL)

	// Create a test server
	server := httptest.NewServer(handler)
	defer server.Close()

	// Make a request to the test server
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Don't follow redirects, we want to check the redirect response
			return http.ErrUseLastResponse
		},
	}

	resp, err := client.Get(server.URL)
	if err != nil {
		t.Fatalf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	// Verify redirect
	if resp.StatusCode != http.StatusSeeOther {
		t.Errorf("expected status %d, got %d", http.StatusSeeOther, resp.StatusCode)
	}

	location := resp.Header.Get("Location")
	if location != logOutURL {
		t.Errorf("expected redirect to %s, got %s", logOutURL, location)
	}

	// Verify cookies are set for clearing
	cookies := resp.Cookies()
	if len(cookies) < 2 {
		t.Errorf("expected at least 2 cookies to be set, got %d", len(cookies))
	}

	for _, cookie := range cookies {
		if strings.HasPrefix(cookie.Name, "AWSELBAuthSessionCookie") {
			if cookie.Value != "" {
				t.Errorf("expected cookie %s to be empty for clearing", cookie.Name)
			}
		}
	}
}
