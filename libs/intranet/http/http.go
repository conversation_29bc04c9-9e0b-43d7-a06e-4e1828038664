// Package http implements the core HTTP application functionality and shared components
// for the SA Intranet system. It provides configuration management, application setup,
// and integration with the Hexa HTTP server framework.
package http

import (
	"errors"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/spf13/viper"
)

// Config holds the HTTP application configuration settings.
// It uses mapstructure tags for automatic configuration binding from environment variables.
type Config struct {
	// BaseURL is the base URL for the HTTP application (default: http://localhost:8000)
	BaseURL string `mapstructure:"base_url"`
}

// App represents the main HTTP application structure that wraps the Hexa HTTP server
// with application-specific configuration and functionality.
type App struct {
	// DefaultApp is the underlying Hexa HTTP server application
	DefaultApp httpsvr.App
	// Config contains the application configuration settings
	Config Config
}

// NewApp creates and initializes a new HTTP application instance with configuration
// loaded from environment variables and .env files. It sets up the Viper configuration
// manager and creates the underlying Hexa HTTP server application.
//
// The function performs the following initialization steps:
//   - Sets up Viper configuration with default values
//   - Enables automatic environment variable binding
//   - Attempts to load configuration from .env file
//   - Creates and configures the Hexa HTTP server
//
// Returns a fully configured App instance ready for use.
// Panics if configuration loading or unmarshaling fails.
func NewApp() *App {
	v := viper.New()

	// Set default configuration values
	v.SetDefault("base_url", "http://localhost:8000")

	// Enable automatic environment variable binding
	v.AutomaticEnv()

	// Optional: specify config files (commented out for flexibility)
	// v.SetConfigName("config")
	// v.SetConfigType("yaml")
	// v.AddConfigPath(".")
	// v.AddConfigPath("./config")

	// Configure .env file loading
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(".")

	// Attempt to read configuration file (gracefully handle missing .env)
	if err := v.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError

		// Only panic on actual errors, not missing config files
		if !errors.As(err, &configFileNotFoundError) {
			panic(err)
		}
	}

	// Unmarshal configuration into struct
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		panic(err)
	}

	// Create the application instance with Hexa HTTP server
	app := &App{
		DefaultApp: httpsvr.NewApp(),
		Config:     config,
	}

	return app
}
