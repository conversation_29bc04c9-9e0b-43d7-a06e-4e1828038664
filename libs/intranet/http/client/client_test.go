package client_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"sa-intranet/http/client"
)

func TestDefaultClient(t *testing.T) {
	// Test creating a DefaultClient directly
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	if httpClient == nil {
		t.<PERSON><PERSON>("DefaultClient creation failed")
	}

	if httpClient.Client == nil {
		t.<PERSON>("DefaultClient.Client is nil")
	}
}

func TestDefaultClientRequest(t *testing.T) {
	// Create a simple test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	// Create client
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	// Test successful request
	resp, err := httpClient.Request(server.URL, "GET", "", nil, nil)
	if err != nil {
		t.<PERSON><PERSON>("Request() unexpected error = %v", err)
		return
	}

	if resp == nil {
		t.<PERSON><PERSON>rf("Request() returned nil response")
		return
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Request() status = %v, want %v", resp.StatusCode, http.StatusOK)
	}

	resp.Body.Close()
}

func TestDefaultClientRequestErrors(t *testing.T) {
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	tests := []struct {
		name    string
		baseURL string
		method  string
		wantErr bool
	}{
		{
			name:    "empty baseURL",
			baseURL: "",
			method:  "GET",
			wantErr: true,
		},
		{
			name:    "invalid method",
			baseURL: "http://example.com",
			method:  "INVALID",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := httpClient.Request(tt.baseURL, tt.method, "", nil, nil)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Request() expected error, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Request() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestDefaultClientRequestJSON(t *testing.T) {
	// Test response structure
	type TestResponse struct {
		Message string `json:"message"`
		Status  string `json:"status"`
	}

	tests := []struct {
		name           string
		serverResponse string
		serverStatus   int
		wantErr        bool
		verify         func(t *testing.T, target TestResponse)
	}{
		{
			name:           "successful_json_response",
			serverResponse: `{"message":"hello","status":"ok"}`,
			serverStatus:   http.StatusOK,
			wantErr:        false,
			verify: func(t *testing.T, target TestResponse) {
				if target.Message != "hello" {
					t.Errorf("expected message 'hello', got %s", target.Message)
				}
				if target.Status != "ok" {
					t.Errorf("expected status 'ok', got %s", target.Status)
				}
			},
		},
		{
			name:           "invalid_json_response",
			serverResponse: `{"message":"hello","status":}`, // Invalid JSON
			serverStatus:   http.StatusOK,
			wantErr:        true,
		},
		{
			name:           "server_error_response",
			serverResponse: `{"error":"internal server error"}`,
			serverStatus:   http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:           "empty_json_response",
			serverResponse: `{}`,
			serverStatus:   http.StatusOK,
			wantErr:        false,
			verify: func(t *testing.T, target TestResponse) {
				if target.Message != "" {
					t.Errorf("expected empty message, got %s", target.Message)
				}
				if target.Status != "" {
					t.Errorf("expected empty status, got %s", target.Status)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(tt.serverStatus)
				w.Write([]byte(tt.serverResponse))
			}))
			defer server.Close()

			// Create client
			httpClient := &client.DefaultClient{
				Client: &http.Client{},
			}

			// Test RequestJSON
			var target TestResponse
			err := httpClient.RequestJSON(server.URL, "GET", "", nil, &target, nil)

			if (err != nil) != tt.wantErr {
				t.Errorf("RequestJSON() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, target)
			}
		})
	}
}

func TestDefaultClientRequestJSONWithApplier(t *testing.T) {
	// Test that applier function is called correctly
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if custom header was set by applier
		if r.Header.Get("X-Custom-Header") != "test-value" {
			t.Errorf("expected custom header to be set")
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message":"success"}`))
	}))
	defer server.Close()

	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	// Applier function that adds custom header
	applier := func(req *http.Request) {
		req.Header.Set("X-Custom-Header", "test-value")
	}

	type TestResponse struct {
		Message string `json:"message"`
	}

	var target TestResponse
	err := httpClient.RequestJSON(server.URL, "GET", "", nil, &target, applier)
	if err != nil {
		t.Errorf("RequestJSON() with applier error = %v", err)
	}

	if target.Message != "success" {
		t.Errorf("expected message 'success', got %s", target.Message)
	}
}
