// Package client implements a generic HTTP client for external service communication.
// It provides a simplified interface for making HTTP requests with automatic error handling,
// JSON marshaling/unmarshaling, and request customization capabilities.
package client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strings"

	"golang.org/x/exp/slog"
)

// Error definitions for HTTP client operations
var (
	// ErrClientRequired is returned when no HTTP client is provided
	ErrClientRequired = errors.New("client is required")
	// ErrBaseURLRequired is returned when base URL is empty or invalid
	ErrBaseURLRequired = errors.New("baseURL is required")
	// ErrInvalidHTTPMethod is returned when an unsupported HTTP method is used
	ErrInvalidHTTPMethod = errors.New("invalid HTTP method")
	// ErrAPIRequest is returned when the API responds with an error status code
	ErrAPIRequest = errors.New("API request error")
)

// methods contains all supported HTTP methods for validation
var methods = []string{
	http.MethodGet,
	http.MethodPost,
	http.MethodPut,
	http.MethodDelete,
	http.MethodPatch,
	http.MethodHead,
	http.MethodOptions,
	http.MethodTrace,
	http.MethodConnect,
}

// RequestApply is a function type for customizing HTTP requests before they are sent.
// It allows adding headers, authentication, or other request modifications.
type RequestApply func(*http.Request)

// DefaultClient wraps the standard HTTP client with additional functionality
// for simplified API communication and error handling.
type DefaultClient struct {
	// Client is the underlying HTTP client used for making requests
	Client *http.Client
}

// Request performs an HTTP request with the specified parameters and returns the response.
// It validates the base URL and HTTP method, creates the request with proper context,
// applies any custom request modifications, and handles error responses automatically.
//
// Parameters:
//   - baseURL: The base URL for the API endpoint
//   - method: HTTP method (GET, POST, PUT, DELETE, etc.)
//   - path: The API endpoint path to append to baseURL
//   - body: Request body reader (can be nil for GET requests)
//   - applier: Optional function to customize the request (headers, auth, etc.)
//
// Returns the HTTP response or an error if the request fails or returns an error status.
func (dc *DefaultClient) Request(
	baseURL string,
	method string,
	path string,
	body io.Reader,
	applier RequestApply,
) (*http.Response, error) {
	// Validate required parameters
	if strings.TrimSpace(baseURL) == "" {
		return nil, ErrBaseURLRequired
	}

	if !slices.Contains(methods, method) {
		return nil, fmt.Errorf("%w: %s", ErrInvalidHTTPMethod, method)
	}

	// Create HTTP request with background context
	req, err := http.NewRequestWithContext(context.Background(), method, baseURL+path, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Apply custom request modifications if provided
	if applier != nil {
		applier(req)
	}

	// Execute the HTTP request
	resp, err := dc.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to request: %w", err)
	}

	// Handle error status codes (4xx and 5xx)
	if resp.StatusCode >= 400 && resp.StatusCode < 600 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("%w: status %d: %s", ErrAPIRequest, resp.StatusCode, string(bodyBytes))
	}

	return resp, nil
}

// RequestJSON performs an HTTP request and automatically unmarshals the JSON response
// into the provided target struct. It handles the complete request lifecycle including
// response body cleanup and error handling.
//
// Parameters:
//   - baseURL: The base URL for the API endpoint
//   - method: HTTP method (GET, POST, PUT, DELETE, etc.)
//   - path: The API endpoint path to append to baseURL
//   - body: Request body reader (can be nil for GET requests)
//   - target: Pointer to struct where JSON response will be unmarshaled
//   - applier: Optional function to customize the request (headers, auth, etc.)
//
// Returns an error if the request fails or JSON unmarshaling fails.
func (dc *DefaultClient) RequestJSON(
	baseURL string,
	method string,
	path string,
	body io.Reader,
	target any,
	applier RequestApply,
) error {
	// Perform the HTTP request
	resp, err := dc.Request(baseURL, method, path, body, applier)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}

	// Ensure response body is properly closed
	defer func() {
		if err := resp.Body.Close(); err != nil {
			slog.Error("failed to close response body", "error", err)
		}
	}()

	// Decode JSON response into target struct
	if err := json.NewDecoder(resp.Body).Decode(target); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	return nil
}
