# Agent Reference for sa-intranet

## Build/Test/Lint Commands
- `task run-check` - Run linter on all modules
- `task run-test` - Run all tests and generate coverage report
- `task run-check-intranet` - Run linter on libs/intranet only
- `task intranet:run-sonar-scanner-prepare` - Run intranet tests with coverage
- `go test ./... -v -cover` - Run single module tests (from module directory)
- `go test ./path/to/pkg -v` - Run tests for specific package
- `golangci-lint run` - Lint code (use correct config path)

## Architecture
- **Go workspace** with `go.work` managing 4 modules: libs/hexa, libs/intranet, services/backend, services/frontend
- **Clean Architecture + DDD** with hexagonal architecture patterns
- **libs/intranet** - Core domain logic (SonarQube, Jira, Grafana), min 80% test coverage required
- **libs/hexa** - Hexagonal architecture support library
- **services/backend** - REST API service
- **services/frontend** - GoNertia (Go + Inertia.js) with React/TypeScript frontend

## Code Style & Conventions
- Use **Clean Architecture** structure: model, repository, usecase, service, presenter, validator
- **Error handling**: Use `fmt.Errorf` with wrapped errors, avoid dynamic errors
- **Dependencies**: `samber/do` for DI, `viper` for config, `bun` for DB, `validator` for validation
- **Tests**: Use `testcontainers-go` for integration tests, built-in `testing` package
- **Line length**: 160 chars max, use `gofmt` and `goimports` for formatting
- **Naming**: Follow Go conventions, use context.Context for request lifecycle
- **Security**: Use `govulncheck` for vulnerability scanning, never commit secrets
