APP_URL=http://localhost:8000
REGISTRY_URL="{id}.dkr.ecr.{region}.amazonaws.com"
REGISTRY_USER="AWS"
REGISTRY_AWS_REGION="us-east-1"
REGISTRY_AWS_PROFILE=""
IMAGE_NAME=""
IMAGE_TAG="latest"

PAPYRUS_TOKEN=
APP_ENABLE_AUTH_AWS_COGNITO=
APP_AWS_COGNITO_SIGNER=
APP_LOGOUT_URL=
APP_SONARQUBE_URL="https://sonarqube.mycompany.com"
APP_SONARQUBE_TOKEN=""
APP_JIRA_URL="https://mycompany.atlassian.net"
APP_DATABASE_URL="**************************************************/sa_intranet_db?sslmode=disable"
APP_CYPHER_KEY="my_secret_key"	# this must be a base64 encoded string, can use `openssl rand -base64 32` to generate a new one.
APP_DB_SEED_SONARQUBE_PROJECT_KEY=""
APP_DB_SEED_JIRA_TOKEN=""
APP_DEFAULT_PAGE_SIZE=25
APP_SONARQUBE_WEBHOOK_AUTH_USERNAME="app"
APP_SONARQUBE_WEBHOOK_AUTH_PASSWORD="" # this must be a base64 encoded string, can use `openssl rand -base64 48 | tr '+/' '-_' | tr -d '='` to generate a new one.
APP_AUTH_JWT_SECRET_KEY="my_jwt_secret_key"
APP_ENV="dev"
APP_DEVELOPMENT_USER_ID=""