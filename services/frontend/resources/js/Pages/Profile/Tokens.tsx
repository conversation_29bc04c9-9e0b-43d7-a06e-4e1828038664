import { usePage, router } from '@inertiajs/react';
import { ColumnProps } from '@/Types/ColumnProps';
import React, { useState, useEffect } from 'react';
import { Pagination } from '@/Types/Pagination';
import Breadcrumbs from '@/components/Breadcrumbs';
import Table from '@/components/Table';
import Snackbar from '@/components/Snackbar';
import TokenForm from '@/features/profile/components/TokenForm';
import Button from '@/components/Button';
import { ClipboardIcon, TrashIcon } from '@heroicons/react/24/outline';


interface Token {
  id: string;
  token: string;
  expiresAt: string;
  createdAt: string;
}

interface Data {
  tokens: Token[];
  errors: string[];
}

interface EnvProp {
  jiraURL: string;
}

// Define the page props interface
interface PageProps {
  tokens: Token[];
  tokensPagination: Pagination;
  errors: string[];
  env: EnvProp;
  [key: string]: any; // Add an index signature to satisfy the constraint
}

export default function Tokens() {
  const page = usePage<PageProps>();
  const { errors, env, tokens, tokensPagination } = page.props;

  const queryParams = new URLSearchParams(page.url.split('?')[1]);
  const search = queryParams.get('name') || '';
  const pageParam = queryParams.get('page') || 1;
  const [currentPage, setCurrentPage] = useState<number>(pageParam);
  const [userTokens, setUserTokens] = useState<Token[]>(tokens);
  const [errorsState, setErrorsState] = useState(errors);
  const [isTokenModalOpen, setIsTokenModalOpen] = useState(false);

  function fetchTokens(page: number) {
    router.get('/profile/tokens', { page: page }, { preserveState: true, replace: true });
  }

  useEffect(() => {
    setErrorsState(errors);
  }, [errors]);

  const pageChanged = (page: number) => {
    setCurrentPage(page);
    fetchTokens(page);
  };

  useEffect(() => {
    if (tokens !== page.props.tokens) {
      setUserTokens(tokens);
    }
  }, [tokens]);

  const handleRevokeToken = (tokenId: string) => {
    // Call the API to revoke the token
    router.delete(`/profile/tokens/${tokenId}`, {
      preserveState: true,
      preserveUrl: true,
      onSuccess: () => {
        router.reload({ only: ['tokens', 'tokensPagination'] });
      },
      onError: (error) => {
        // Handle error, e.g., show a snackbar with the error message
        setErrorsState([error.message]);
      }
    });
  }
  const columns: Array<ColumnProps<Token>> = [
    {
      key: 'token', title: 'Token',
      render: (column, row) => {
        return (
          <div className='flex items-center max-w-md'>
            <span className='truncate'>{row.token}</span>
            <Button
              className='ml-2 btn btn-sm btn-secondary'
              onClick={() => {
                navigator.clipboard.writeText(row.token);
              }}
              icon={<ClipboardIcon className='stroke-2 w-4 h-4 text-white' />}
            >
              Copy
            </Button>
          </div>
        );
      }
    },
    {
      key: 'expiresAt', title: 'Expires At',
      render: (column, row) => {
        const date = new Date(`${row.expiresAt}T00:00:00`);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
      }
    },
    {
      key: 'createdAt', title: 'Created At',
      render: (column, row) => {
        const date = new Date(row.createdAt);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
      }
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (column, row) => (
        <div className='flex gap-2'>
          <Button label="Revoke" className='btn btn-sm btn-primary' icon={<TrashIcon className='w-4 h-4' />} onClick={() => { handleRevokeToken(row.id) }} />
        </div>
      ),
    },
  ];

  return (
    <div className="mx-auto p-4 container">

      <Breadcrumbs items={[{ title: 'Home', href: '/' }, { title: 'Tokens' }]} />

      <h1 className="mb-6 font-bold text-3xl">Tokens</h1>
      <p className="mb-4">Manage your tokens here.</p>
      <div className="flex flex-col gap-2">
        {errorsState &&errorsState.length > 0 && (
          <Snackbar message={errorsState[0]} type='error' autoDismiss={false} title='Error' uniqueKey={new Date().getTime()} />
        )}
        <button
          className='self-end btn btn-primary'
          onClick={() => {
            setIsTokenModalOpen(true);
          }}
        >
          Create Token
        </button>
        {isTokenModalOpen && (
          <TokenForm
            onClose={() => setIsTokenModalOpen(false)}
          />
        )}
        <Table
          data={tokens}
          columns={columns}
          pagination={tokensPagination}
          onPageChange={(page: number) => pageChanged(page)}
          currentPage={currentPage}
        />
      </div>
    </div>
  );
}