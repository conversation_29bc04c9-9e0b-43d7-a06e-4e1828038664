import React from 'react';
import { Link } from '@inertiajs/react';
import Breadcrumbs from '@/components/Breadcrumbs';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers } from '@fortawesome/free-solid-svg-icons';
import { ClipboardIcon, CogIcon } from '@heroicons/react/24/outline';


interface ProfileSection {
  title: string;
  description: string;
  icon: React.ForwardRefExoticComponent<React.SVGProps<SVGSVGElement>>; // Changed to accept FontAwesome icon objects
  href: string;
}

export default function Profile() {
  // Define profile sections - easy to add more in the future
  const profileSections: ProfileSection[] = [
    {
      title: 'Tokens',
      description: 'Manage your tokens',
      icon: <CogIcon className='w-8'/>, // Use FontAwesomeIcon component
      href: '/profile/tokens',
    },
  ];

  return (
    <div className='mx-auto p-4 container'>
      <Breadcrumbs items={[{ title: 'Home', href: '/' }, { title: 'Profile' }]} />

      <div className="mt-6">
        <h1 className="mb-6 font-bold text-3xl">My Profile</h1>

        <div className="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {profileSections.map((section) => (
            <Link
              key={section.title}
              href={section.href}
              
              className="bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300 card"
            >
              <div className="card-body">
                <div className="flex items-center gap-4">
                  <div className="bg-opacity-10 p-3 rounded-lg text-primary">
                    {section.icon}
                  </div>
                  <div>
                    <h2 className="card-title">{section.title}</h2>
                    <p className="opacity-70 text-sm">{section.description}</p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
