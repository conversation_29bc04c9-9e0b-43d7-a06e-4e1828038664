package ui

import (
	"context"
	"fmt"
	"log"
	"os"
	intranet "sa-intranet"
	intranetConfig "sa-intranet/config"
	"sync"

	"github.com/emilioforrer/hexa/cmd"
	"github.com/samber/do"

	assets "app/frontend"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/romsar/gonertia/v2"
	"github.com/spf13/viper"
)

type AppConfig struct {
	Intranet                     intranetConfig.Config `mapstructure:",squash"` // <--- important!
	Environment                  string                `mapstructure:"APP_ENV"`
	ServerAddress                string                `mapstructure:"APP_SERVER_ADDRESS"`
	SSREnabled                   bool                  `mapstructure:"APP_SSR_ENABLED"`
	AuthAwsCognitoEnabled        bool                  `mapstructure:"APP_ENABLE_AUTH_AWS_COGNITO"`
	AwsCognitoSigner             string                `mapstructure:"APP_AWS_COGNITO_SIGNER"`
	DevelopmentUserID            string                `mapstructure:"APP_DEVELOPMENT_USER_ID"` // Used for development purposes
	AwsCognitoLogoutURL          string                `mapstructure:"APP_LOGOUT_URL"`
	SonarQubeWebhookAuthUsername string                `mapstructure:"APP_SONARQUBE_WEBHOOK_AUTH_USERNAME"`
	SonarQubeWebhookAuthPassword string                `mapstructure:"APP_SONARQUBE_WEBHOOK_AUTH_PASSWORD"`
}

func NewAppConfig() (*AppConfig, error) {
	v := viper.New()

	// Get config path from env or fallback to cwd
	configPath := os.Getenv("APP_CONFIG_PATH")
	if configPath == "" {
		configPath, _ = os.Getwd() // Fallback to cwd if CONFIG_PATH is unset
	}

	// Set default values
	v.SetDefault("APP_SERVER_ADDRESS", "0.0.0.0:8000")
	v.SetDefault("APP_SSR_ENABLED", false)
	v.SetDefault("APP_ENV", "dev")
	v.SetDefault("APP_ENABLE_AUTH_AWS_COGNITO", false)
	v.SetDefault("APP_AWS_COGNITO_SIGNER", "changeme")
	v.SetDefault("APP_LOGOUT_URL", "https://changeme.com/logout")
	v.SetDefault("APP_SONARQUBE_WEBHOOK_AUTH_USERNAME", "changeme")
	v.SetDefault("APP_SONARQUBE_WEBHOOK_AUTH_PASSWORD", "")

	// Enable environment variables
	v.AutomaticEnv()

	// Read from .env file if exists
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(configPath)

	// Ignore error if config file is not found
	_ = v.ReadInConfig()

	config := &AppConfig{}
	if err := v.Unmarshal(config); err != nil {
		return nil, err
	}

	// log.Printf("Loaded config: %+v", config)

	iConfig, err := intranetConfig.NewConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to create intranet config: %w", err)
	}

	config.Intranet = *iConfig

	// log.Printf("Final config: %+v", config)

	return config, nil
}

type App struct {
	DefaultApp *httpsvr.DefaultApp
	inertia    *gonertia.Inertia
	injector   *do.Injector
	AppConfig  *AppConfig
	eFS        assets.AssetFS
	apps       []cmd.Runner
	appsWg     sync.WaitGroup
	booted     bool
	registered bool
}

func (a *App) Router() httpsvr.Router {
	return a.DefaultApp.Router
}

func (a *App) Inertia() *gonertia.Inertia {
	return a.inertia
}

func (a *App) Config() httpsvr.DefaultConfig {
	return a.DefaultApp.Config
}

func (a *App) EFS() assets.AssetFS {
	return a.eFS
}

func (a *App) Run() error {
	if !a.registered {
		err := a.Register()
		if err != nil {
			return err
		}

		a.registered = true
	}

	if !a.booted {
		err := a.Boot()
		if err != nil {
			return err
		}

		a.booted = true
	}

	err := a.RunApps(context.Background())
	if err != nil {
		return err
	}

	a.appsWg.Wait()

	return a.DefaultApp.Run()
}

func (a *App) AddApp(app cmd.Runner) {
	a.apps = append(a.apps, app)
}

func (a *App) StopApps(ctx context.Context) error {
	for _, app := range a.apps {
		err := app.Stop(ctx)
		if err != nil {
			log.Println(err)
		}
	}

	return nil
}

func (a *App) Stop(ctx context.Context) error {
	_ = a.StopApps(ctx)
	err := a.DefaultApp.Stop()

	return err
}

func (a *App) RunApps(ctx context.Context) error {
	for _, app := range a.apps {
		appName := app.Config().Name
		ssrEnabled := a.AppConfig.SSREnabled

		switch {
		case appName == "ssr" && ssrEnabled:
			a.appsWg.Add(1)
			// Run in goroutine but handle signals in main thread
			errChan := make(chan error, 1)
			go func() {
				errChan <- app.Run(ctx)
			}()

			// Wait for health check
			select {
			case <-app.Healthy():
				a.appsWg.Done()
			case err := <-errChan:
				a.appsWg.Done()
				return err
			}
		default:
			return nil
		}
	}

	return nil
}

func (a *App) Injector() *do.Injector {
	return a.injector
}

func (a *App) Boot() error {
	if !a.booted {
		a.booted = true
	}

	return nil
}

func (a *App) Register() error {
	if !a.registered {
		a.registered = true
		// Initialize the injector
		err := intranet.Register(a.injector, a.AppConfig.Intranet)
		if err != nil {
			return err
		}
	}

	return nil
}

func NewApp(injector *do.Injector) *App {
	config, err := NewAppConfig()
	if err != nil {
		log.Fatal(err)
	}

	ssrRunner := NewSSRApp()

	eFS := assets.EmbedFS()

	i := InitInertia(eFS)

	app := &App{
		DefaultApp: httpsvr.NewApp(),
		inertia:    i,
		eFS:        eFS,
		AppConfig:  config,
		injector:   injector,
	}

	app.DefaultApp.Config.Addr = app.AppConfig.ServerAddress

	app.AddApp(ssrRunner)

	return app
}
