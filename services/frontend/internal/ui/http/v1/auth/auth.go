// Package auth used for kubernetes auth check
package auth

import (
	"net/http"
	"sa-intranet/http/auth"

	"app/frontend/internal/ui"
	"app/frontend/internal/ui/http/v1/auth/profile"
)

func RegisterRoutes(app *ui.App) {
	logOutHandler := func(w http.ResponseWriter, r *http.Request) {
		auth.LogoutHandler(app.AppConfig.AwsCognitoLogoutURL).ServeHTTP(w, r)
	}

	profile.RegisterRoutes(app)
	app.Router().GET("/logout", logOutHandler)
}
