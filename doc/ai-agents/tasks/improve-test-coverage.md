

- Read guidelines from .augment-guidelines and doc/ai-agents/prompts/improve-test-coverage.md
- Check current test coverage for libs/intranet module
- Analyze existing test structure in libs/intranet
- Identify code that needs test coverage
- Implement missing tests to reach 80% coverage and focus on those first
- Verify final test coverage meets 80% target by running `task intranet:run-sonar-scanner-prepare`